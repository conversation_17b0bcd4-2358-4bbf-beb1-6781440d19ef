# app/schemas/technical_document.py
from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from app.models.document import DocumentType

class TechnicalDocumentBase(BaseModel):
    """Schéma de base pour les documents techniques"""
    name: Optional[str] = None
    type_document: Optional[DocumentType] = None
    content: Optional[str] = None  # Contenu HTML
    lot_id: Optional[int] = None

class TechnicalDocumentCreate(TechnicalDocumentBase):
    """Schéma pour la création d'un document technique"""
    name: str = Field(..., min_length=1, max_length=255)
    type_document: DocumentType
    lot_id: int
    company_ids: Optional[List[int]] = Field(default_factory=list, description="IDs des companies à associer")

class TechnicalDocumentUpdate(TechnicalDocumentBase):
    """Schéma pour la mise à jour d'un document technique"""
    company_ids: Optional[List[int]] = Field(None, description="IDs des companies à associer (remplace les existantes)")

class TechnicalDocumentCompanyBase(BaseModel):
    """Schéma de base pour la relation document-company"""
    technical_document_id: int
    company_id: int

class TechnicalDocumentCompanyCreate(TechnicalDocumentCompanyBase):
    """Schéma pour créer une relation document-company"""
    pass

class TechnicalDocumentCompanyResponse(TechnicalDocumentCompanyBase):
    """Schéma de réponse pour la relation document-company"""
    id: int
    created_at: datetime

    class Config:
        from_attributes = True

class CompanySimple(BaseModel):
    """Schéma simplifié pour les companies dans les réponses"""
    id: int
    name: str
    code: Optional[str] = None

    class Config:
        from_attributes = True

class LotSimple(BaseModel):
    """Schéma simplifié pour les lots dans les réponses"""
    id: int
    name: str
    code: str
    project_id: int

    class Config:
        from_attributes = True

class ProjectSimple(BaseModel):
    """Schéma simplifié pour les projets dans les réponses"""
    id: int
    name: str
    code: Optional[str] = None

    class Config:
        from_attributes = True

class UserSimple(BaseModel):
    """Schéma simplifié pour les utilisateurs dans les réponses"""
    id: int
    email: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None

    class Config:
        from_attributes = True

class TechnicalDocumentInDBBase(TechnicalDocumentBase):
    """Schéma de base avec les champs de base de données"""
    id: Optional[int] = None
    created_by: Optional[int] = None
    updated_by: Optional[int] = None
    is_active: Optional[bool] = True
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class TechnicalDocumentList(TechnicalDocumentInDBBase):
    """Schéma pour les listes de documents (sans contenu HTML complet)"""
    lot: Optional[LotSimple] = None
    creator: Optional[UserSimple] = None
    company_count: Optional[int] = Field(0, description="Nombre de companies associées")

class TechnicalDocumentResponse(TechnicalDocumentInDBBase):
    """Schéma de réponse complet avec relations"""
    lot: Optional[LotSimple] = None
    creator: Optional[UserSimple] = None
    updater: Optional[UserSimple] = None
    companies: List[CompanySimple] = Field(default_factory=list)

class TechnicalDocumentWithCompanies(TechnicalDocumentInDBBase):
    """Schéma avec les relations companies détaillées"""
    companies: List[TechnicalDocumentCompanyResponse] = Field(default_factory=list)

# Schémas pour l'amélioration de texte avec ChatGPT
class TextEnhancementRequest(BaseModel):
    """Schéma pour les demandes d'amélioration de texte"""
    text: str = Field(..., min_length=1, description="Texte à améliorer")
    prompt_type: str = Field(..., description="Type de prompt (enhance, rephrase, expand, simplify)")
    document_type: DocumentType = Field(..., description="Type de document pour adapter le prompt")
    context: Optional[str] = Field(None, description="Contexte supplémentaire pour l'amélioration")

class TextEnhancementResponse(BaseModel):
    """Schéma de réponse pour l'amélioration de texte"""
    original_text: str
    enhanced_text: str
    prompt_type: str
    document_type: DocumentType
    processing_time: Optional[float] = None

# Schémas pour les filtres et recherche
class TechnicalDocumentFilter(BaseModel):
    """Schéma pour filtrer les documents techniques"""
    lot_id: Optional[int] = None
    project_id: Optional[int] = None  # Garde pour compatibilité - filtre via les lots
    type_document: Optional[DocumentType] = None
    workspace_id: Optional[int] = None
    created_by: Optional[int] = None
    is_active: Optional[bool] = True
    search_term: Optional[str] = Field(None, description="Recherche dans le nom et contenu")

class TechnicalDocumentSearchResponse(BaseModel):
    """Schéma de réponse pour la recherche de documents"""
    documents: List[TechnicalDocumentList]
    total_count: int
    page: int = 1
    page_size: int = 20
    total_pages: int

# Schémas pour les statistiques
class TechnicalDocumentStats(BaseModel):
    """Schéma pour les statistiques des documents techniques"""
    total_documents: int = 0
    cctp_count: int = 0
    dpgf_count: int = 0
    active_documents: int = 0

# Schémas pour la génération d'articles
class ArticleGenerationRequest(BaseModel):
    """Schéma pour les demandes de génération d'articles CCTP"""
    parent_id: Optional[str] = Field(None, description="ID du heading parent")
    prestation: str = Field(..., min_length=1, description="Description de la prestation")
    localisation: Optional[str] = Field(None, description="Localisation de l'ouvrage")
    marque: Optional[str] = Field(None, description="Marque ou équivalent")
    reference: Optional[str] = Field(None, description="Référence technique")
    nature: Optional[str] = Field(None, description="Nature des matériaux")
    criteres_qualite: Optional[str] = Field(None, description="Critères qualité")
    dimensions: Optional[str] = Field(None, description="Dimensions")
    couleur: Optional[str] = Field(None, description="Couleur")
    particularite: Optional[str] = Field(None, description="Particularité de la prestation")
    description_pose: Optional[str] = Field(None, description="Description mise en œuvre")
    type_pose: Optional[str] = Field(None, description="Type de pose")
    marque_pose: Optional[str] = Field(None, description="Marque matériels pose")
    reference_pose: Optional[str] = Field(None, description="Référence technique pose")
    inclure_criteres: bool = Field(True, description="Inclure § 4 (essais)")
    inclure_docs: bool = Field(True, description="Inclure § 5 (docs)")
    unite: str = Field("u", description="Unité de mesure")
    quantite: float = Field(1.0, ge=0, description="Quantité")

class ArticleGenerationResponse(BaseModel):
    """Schéma de réponse pour la génération d'articles"""
    title: str = Field(..., description="Titre de l'article généré")
    body_markdown: str = Field(..., description="Corps de l'article en Markdown")
    body_html: str = Field(..., description="Corps de l'article en HTML")
    article_number: Optional[str] = Field(None, description="Numéro d'article calculé")
    parent_id: Optional[str] = Field(None, description="ID du heading parent")
    processing_time: Optional[float] = None
    documents_by_project: dict = Field(default_factory=dict)
