import React from 'react'
import { Editor } from '@tiptap/react'

interface EditorToolbarProps {
  editor: Editor | null
  onInsertHeading: (level: number) => void
  onShowImportModal: () => void
  onShowAddArticleModal: () => void
}

export default function EditorToolbar({ 
  editor, 
  onInsertHeading, 
  onShowImportModal, 
  onShowAddArticleModal 
}: EditorToolbarProps) {
  if (!editor) return null

  return (
    <div className="google-toolbar">
      <div className="toolbar-single-row">
        {/* Historique */}
        <div className="toolbar-section">
          <button
            onClick={() => editor.chain().focus().undo().run()}
            disabled={!editor.can().undo()}
            className="toolbar-btn"
            title="Annuler (Ctrl+Z)"
          >
            ↶
          </button>
          <button
            onClick={() => editor.chain().focus().redo().run()}
            disabled={!editor.can().redo()}
            className="toolbar-btn"
            title="Refaire (Ctrl+Y)"
          >
            ↷
          </button>
        </div>

        {/* Sélecteur de titres */}
        <div className="toolbar-section">
          <select
            onChange={(e) => {
              const value = e.target.value
              if (value === '0') {
                editor.chain().focus().setParagraph().run()
              } else {
                const level = parseInt(value)
                onInsertHeading(level)
              }
              // Reset du sélecteur
              e.target.value = '0'
            }}
            className="font-selector"
            defaultValue="0"
          >
            <option value="0">Paragraphe</option>
            <option value="1">1.</option>
            <option value="2">1.1</option>
            <option value="3">1.1.1</option>
            <option value="4">1.1.1.1</option>
            <option value="5">1.1.1.1.1</option>
            <option value="6">1.1.1.1.1.a</option>
          </select>
        </div>

        {/* Formatage de base */}
        <div className="toolbar-section">
          <button
            onClick={() => editor.chain().focus().toggleBold().run()}
            className={`toolbar-btn ${editor.isActive('bold') ? 'active' : ''}`}
            title="Gras (Ctrl+B)"
          >
            <strong>B</strong>
          </button>
          <button
            onClick={() => editor.chain().focus().toggleItalic().run()}
            className={`toolbar-btn ${editor.isActive('italic') ? 'active' : ''}`}
            title="Italique (Ctrl+I)"
          >
            <em>I</em>
          </button>
          <button
            onClick={() => editor.chain().focus().toggleUnderline().run()}
            className={`toolbar-btn ${editor.isActive('underline') ? 'active' : ''}`}
            title="Souligné (Ctrl+U)"
          >
            <u>U</u>
          </button>
          <button
            onClick={() => editor.chain().focus().toggleStrike().run()}
            className={`toolbar-btn ${editor.isActive('strike') ? 'active' : ''}`}
            title="Barré"
          >
            <s>S</s>
          </button>
        </div>

        {/* Couleur de texte */}
        <div className="toolbar-section">
          <input
            type="color"
            defaultValue="#000000"
            onChange={(e) => editor.chain().focus().setColor(e.target.value).run()}
            className="color-btn"
            title="Couleur du texte"
          />
          <button
            onClick={() => editor.chain().focus().toggleHighlight().run()}
            className={`toolbar-btn ${editor.isActive('highlight') ? 'active' : ''}`}
            title="Surligner"
          >
            🖍️
          </button>
        </div>

        {/* Listes */}
        <div className="toolbar-section">
          <button
            onClick={() => editor.chain().focus().toggleBulletList().run()}
            className={`toolbar-btn ${editor.isActive('bulletList') ? 'active' : ''}`}
            title="Liste à puces"
          >
            •
          </button>
          <button
            onClick={() => editor.chain().focus().toggleOrderedList().run()}
            className={`toolbar-btn ${editor.isActive('orderedList') ? 'active' : ''}`}
            title="Liste numérotée"
          >
            1.
          </button>
        </div>

        {/* Indentation */}
        <div className="toolbar-section">
          <button
            onClick={() => editor.chain().focus().liftListItem('listItem').run()}
            className="toolbar-btn"
            title="Diminuer l'indentation"
          >
            ⬅
          </button>
          <button
            onClick={() => editor.chain().focus().sinkListItem('listItem').run()}
            className="toolbar-btn"
            title="Augmenter l'indentation"
          >
            ➡
          </button>
        </div>

        {/* Alignement */}
        <div className="toolbar-section">
          <button
            onClick={() => editor.chain().focus().setTextAlign('left').run()}
            className={`toolbar-btn ${editor.isActive({ textAlign: 'left' }) ? 'active' : ''}`}
            title="Aligner à gauche"
          >
            ⬅
          </button>
          <button
            onClick={() => editor.chain().focus().setTextAlign('center').run()}
            className={`toolbar-btn ${editor.isActive({ textAlign: 'center' }) ? 'active' : ''}`}
            title="Centrer"
          >
            ↔
          </button>
          <button
            onClick={() => editor.chain().focus().setTextAlign('right').run()}
            className={`toolbar-btn ${editor.isActive({ textAlign: 'right' }) ? 'active' : ''}`}
            title="Aligner à droite"
          >
            ➡
          </button>
          <button
            onClick={() => editor.chain().focus().setTextAlign('justify').run()}
            className={`toolbar-btn ${editor.isActive({ textAlign: 'justify' }) ? 'active' : ''}`}
            title="Justifier"
          >
            ⬌
          </button>
        </div>

        {/* Insertion */}
        <div className="toolbar-section">
          <button
            onClick={() => {
              const url = window.prompt('URL du lien:')
              if (url) {
                editor.chain().focus().setLink({ href: url }).run()
              }
            }}
            className="toolbar-btn"
            title="Insérer un lien"
          >
            🔗
          </button>
          <button
            onClick={() => editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run()}
            className="toolbar-btn"
            title="Insérer un tableau"
          >
            📊
          </button>
          <button
            onClick={onShowImportModal}
            className="toolbar-btn"
            title="Importer un document Word"
          >
            📄
          </button>
        </div>

        {/* Ajouter un article */}
        <div className="toolbar-section">
          <button
            onClick={onShowAddArticleModal}
            className="toolbar-btn article-btn"
            title="Ajouter un article CCTP"
          >
            ➕ Article
          </button>
        </div>
      </div>
    </div>
  )
}
