import React from 'react'

interface EditorSidebarProps {
  lotId?: number | null
  isSaving: boolean
  hasUnsavedChanges: boolean
  onSave: () => void
  onClose: () => void
}

export default function EditorSidebar({ 
  lotId, 
  isSaving, 
  hasUnsavedChanges, 
  onSave, 
  onClose 
}: EditorSidebarProps) {
  return (
    <div className="simple-sidebar">
      <div className="lot-info">
        <div className="lot-name">Lot {lotId || 'N/A'}</div>
        <div className="lot-subtitle">Document technique</div>
      </div>
      
      <div className="sidebar-actions">
        <button
          onClick={onSave}
          disabled={isSaving}
          className={`sidebar-btn save-btn ${hasUnsavedChanges ? 'unsaved' : ''}`}
          title="Sauvegarder le document"
        >
          {isSaving ? '⏳ Sauvegarde...' : '💾 Sauvegarder'}
        </button>
        
        <button
          onClick={onClose}
          className="sidebar-btn close-btn"
          title="Fermer l'éditeur"
        >
          ✕ Fermer
        </button>
      </div>
    </div>
  )
}
