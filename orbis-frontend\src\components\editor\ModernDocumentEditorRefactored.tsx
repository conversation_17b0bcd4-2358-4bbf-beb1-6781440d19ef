'use client'

import React from 'react'
import { EditorContent } from '@tiptap/react'
import { useEditorSetup } from './hooks/useEditorSetup'
import { useEditorActions } from './hooks/useEditorActions'
import EditorToolbar from './components/EditorToolbar'
import EditorSidebar from './components/EditorSidebar'
import AddArticleModal from './AddArticleModal'
import { DocumentType } from '@/types/technical-document'
import './styles/modern-editor.css'

interface ModernDocumentEditorProps {
  value: string
  onChange: (content: string) => void
  documentType: DocumentType
  onTextSelection?: (selectedText: string) => void
  readOnly?: boolean
  workspaceName?: string
  workspaceLogo?: string
  onAddArticle?: (articleData: any) => void
  onSave?: () => Promise<void>
  onClose?: () => void
  hasUnsavedChanges?: boolean
  lotId?: number | null
}

export default function ModernDocumentEditor({
  value,
  onChange,
  documentType,
  onTextSelection,
  readOnly = false,
  workspaceName = '',
  workspaceLogo = '',
  onAddArticle,
  onSave,
  onClose,
  hasUnsavedChanges = false,
  lotId
}: ModernDocumentEditorProps) {
  
  // Configuration de l'éditeur
  const { editor } = useEditorSetup({
    value,
    onChange,
    onTextSelection,
    readOnly
  })

  // Actions de l'éditeur
  const {
    isSaving,
    showCloseConfirm,
    showImportModal,
    showAddArticleModal,
    availableHeadings,
    setShowImportModal,
    setShowAddArticleModal,
    insertNumberedHeading,
    initializeCCTPStructure,
    handleSave,
    handleClose,
    confirmClose,
    cancelClose,
    handleFileImport,
    fileInputRef
  } = useEditorActions({
    editor,
    onSave,
    onClose,
    hasUnsavedChanges
  })

  if (!editor) {
    return (
      <div className="google-docs-editor">
        <div className="loading-editor">
          <p>Chargement de l'éditeur...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="google-docs-editor">
      {/* Zone principale d'édition */}
      <div className="editor-main-area">
        {/* Sidebar simple à gauche */}
        <EditorSidebar
          lotId={lotId}
          isSaving={isSaving}
          hasUnsavedChanges={hasUnsavedChanges}
          onSave={handleSave}
          onClose={handleClose}
        />

        {/* Zone d'édition principale */}
        <div className="editor-main">
          {/* Barre d'outils */}
          <EditorToolbar
            editor={editor}
            onInsertHeading={insertNumberedHeading}
            onShowImportModal={() => setShowImportModal(true)}
            onShowAddArticleModal={() => setShowAddArticleModal(true)}
          />

          {/* Zone d'édition */}
          <div className="editor-content">
            <EditorContent editor={editor} />
          </div>
        </div>
      </div>

      {/* Modales */}
      {showAddArticleModal && (
        <AddArticleModal
          isOpen={showAddArticleModal}
          onClose={() => setShowAddArticleModal(false)}
          onSubmit={(articleData) => {
            onAddArticle?.(articleData)
            setShowAddArticleModal(false)
          }}
          documentType={documentType}
          availableHeadings={availableHeadings}
        />
      )}

      {/* Modal de confirmation de fermeture */}
      {showCloseConfirm && (
        <div className="modal-overlay">
          <div className="modal-content">
            <h3>Modifications non sauvegardées</h3>
            <p>Vous avez des modifications non sauvegardées. Voulez-vous vraiment fermer ?</p>
            <div className="modal-actions">
              <button onClick={cancelClose} className="btn-secondary">
                Annuler
              </button>
              <button onClick={confirmClose} className="btn-danger">
                Fermer sans sauvegarder
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal d'import */}
      {showImportModal && (
        <div className="modal-overlay">
          <div className="modal-content">
            <h3>Importer un document</h3>
            <input
              ref={fileInputRef}
              type="file"
              accept=".docx,.doc,.txt,.html"
              onChange={handleFileImport}
              className="file-input"
            />
            <div className="modal-actions">
              <button onClick={() => setShowImportModal(false)} className="btn-secondary">
                Annuler
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
