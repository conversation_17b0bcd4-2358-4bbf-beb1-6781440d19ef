'use client'

import { useState, ReactNode } from 'react'
import { 
  Squares2X2Icon,
  ListBulletIcon,
  PlusIcon
} from '@heroicons/react/24/outline'
import Link from 'next/link'

export interface FilterField {
  key: string
  label: string
  type: 'text' | 'select' | 'date'
  options?: { value: string; label: string }[]
  placeholder?: string
}

export interface DataListAction {
  label: string
  onClick: () => void
  icon?: ReactNode
  variant?: 'primary' | 'secondary'
}

export interface DataListItem {
  id: string | number
  [key: string]: any
}

interface DataListProps<T extends DataListItem> {
  // Données
  items: T[]
  loading?: boolean
  
  // Configuration d'affichage
  title: string
  subtitle?: string
  emptyMessage?: string
  emptyIcon?: ReactNode
  
  // Navigation
  getItemUrl: (item: T) => string
  
  // Filtres
  filters: FilterField[]
  onFiltersChange: (filters: Record<string, string>) => void
  
  // Actions
  actions?: DataListAction[]
  
  // Rendu des éléments
  renderGridItem: (item: T) => ReactNode
  renderListItem: (item: T) => ReactNode
  
  // Configuration
  defaultViewMode?: 'grid' | 'list'
  gridCols?: 'auto' | 1 | 2 | 3 | 4 | 5 | 6
}

export default function DataList<T extends DataListItem>({
  items,
  loading = false,
  title,
  subtitle,
  emptyMessage = "Aucun élément trouvé",
  emptyIcon,
  getItemUrl,
  filters,
  onFiltersChange,
  actions = [],
  renderGridItem,
  renderListItem,
  defaultViewMode = 'grid',
  gridCols = 3
}: DataListProps<T>) {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>(defaultViewMode)
  const [filterValues, setFilterValues] = useState<Record<string, string>>({})

  const handleFilterChange = (key: string, value: string) => {
    const newFilters = { ...filterValues, [key]: value }
    setFilterValues(newFilters)
    onFiltersChange(newFilters)
  }

  const getGridColsClass = () => {
    if (gridCols === 'auto') return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
    const colsMap = {
      1: 'grid-cols-1',
      2: 'grid-cols-1 md:grid-cols-2',
      3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
      4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
      5: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5',
      6: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 3xl:grid-cols-6'
    }
    return colsMap[gridCols] || colsMap[3]
  }

  if (loading) {
    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
            {subtitle && <p className="text-gray-600 mt-1">{subtitle}</p>}
          </div>
        </div>

        {/* Loading */}
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Chargement...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
          {subtitle && <p className="text-gray-600 mt-1">{subtitle}</p>}
        </div>
        
        <div className="flex items-center gap-3">
          {/* Toggle vue grille/liste */}
          <div className="flex border border-gray-300 rounded-lg overflow-hidden">
            <button
              onClick={() => setViewMode('grid')}
              className={`px-3 py-2 ${viewMode === 'grid' ? 'bg-primary-600 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'} transition-colors`}
              title="Vue en grille"
            >
              <Squares2X2Icon className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`px-3 py-2 ${viewMode === 'list' ? 'bg-primary-600 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'} transition-colors`}
              title="Vue en liste"
            >
              <ListBulletIcon className="w-4 h-4" />
            </button>
          </div>

          {/* Actions */}
          {actions.map((action, index) => (
            <button
              key={index}
              onClick={action.onClick}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                action.variant === 'primary' 
                  ? 'bg-primary-600 hover:bg-primary-700 text-white' 
                  : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
              }`}
            >
              {action.icon}
              {action.label}
            </button>
          ))}
        </div>
      </div>

      {/* Filtres */}
      {filters.length > 0 && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Filtres</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {filters.map((filter) => (
              <div key={filter.key}>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {filter.label}
                </label>
                {filter.type === 'select' ? (
                  <select
                    value={filterValues[filter.key] || ''}
                    onChange={(e) => handleFilterChange(filter.key, e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  >
                    <option value="">Tous</option>
                    {filter.options?.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                ) : (
                  <input
                    type={filter.type}
                    value={filterValues[filter.key] || ''}
                    onChange={(e) => handleFilterChange(filter.key, e.target.value)}
                    placeholder={filter.placeholder}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Liste des éléments */}
      {items.length === 0 ? (
        <div className="text-center py-12">
          {emptyIcon && <div className="mx-auto h-12 w-12 text-gray-400 mb-4">{emptyIcon}</div>}
          <h3 className="mt-2 text-sm font-medium text-gray-900">Aucun élément</h3>
          <p className="mt-1 text-sm text-gray-500">{emptyMessage}</p>
          {actions.length > 0 && (
            <div className="mt-6">
              <button
                onClick={actions[0].onClick}
                className="inline-flex items-center px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
              >
                {actions[0].icon}
                {actions[0].label}
              </button>
            </div>
          )}
        </div>
      ) : viewMode === 'grid' ? (
        // Vue en grille
        <div className={`grid ${getGridColsClass()} gap-6`}>
          {items.map((item) => (
            <Link key={item.id} href={getItemUrl(item)} className="block">
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 hover:shadow-lg transition-all duration-200 hover:scale-[1.02] cursor-pointer">
                {renderGridItem(item)}
              </div>
            </Link>
          ))}
        </div>
      ) : (
        // Vue en liste
        <div className="space-y-4">
          {items.map((item) => (
            <Link key={item.id} href={getItemUrl(item)} className="block">
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 hover:shadow-lg transition-all duration-200 hover:scale-[1.01] cursor-pointer">
                {renderListItem(item)}
              </div>
            </Link>
          ))}
        </div>
      )}
    </div>
  )
}
