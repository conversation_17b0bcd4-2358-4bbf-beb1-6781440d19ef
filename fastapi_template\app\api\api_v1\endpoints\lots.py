# app/api/api_v1/endpoints/lots.py
"""
API endpoints pour les Lots
"""

from typing import List, Optional, Any, Dict
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func
from sqlalchemy.orm import selectinload
from datetime import datetime

from app.api import deps
from app.models.user import User
from app.models.workspace import UserWorkspace, Workspace
from app.models.lot import Lot, Stakeholder, LotDocument, LotPhase
from app.models.tcompany import TCompany
from app.models.project import Project
from app.models.document import TechnicalDocument
from app.schemas.lot import (
    LotCreate,
    LotUpdate,
    LotResponse,
    LotPhaseValidation,
    LotIntervenantCreate,
    LotIntervenantResponse,
    LotDocumentCreate,
    LotDocumentResponse,
    LotStats,
    BulkLotOperation,
    BulkLotResponse
)
from app.middleware.auth_sync_middleware import require_auth

router = APIRouter()


@router.get("/", response_model=List[LotResponse])
async def get_lots(
    db: AsyncSession = Depends(deps.get_db),
    skip: int = Query(0, ge=0, description="Nombre d'éléments à ignorer"),
    limit: int = Query(100, ge=1, le=1000, description="Nombre maximum d'éléments à retourner"),
    search: Optional[str] = Query(None, description="Recherche dans nom, code, description"),
    project_id: Optional[int] = Query(None, description="Filtrer par projet"),
    phase: Optional[LotPhase] = Query(None, description="Filtrer par phase"),
    is_active: Optional[bool] = Query(None, description="Filtrer par statut actif/inactif"),
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """Récupérer la liste des lots avec pagination et filtres"""
    try:
        print(f"🔍 GET /lots - User: {current_user}")
        
        user_id = current_user.get("id") or current_user.get("user_id")
        if not user_id:
            raise HTTPException(status_code=400, detail="Token utilisateur invalide")

        # Récupérer l'espace de travail de l'utilisateur
        user_workspace_result = await db.execute(
            select(UserWorkspace).where(
                and_(
                    UserWorkspace.user_id == user_id,
                    UserWorkspace.is_active == True
                )
            ).limit(1)
        )
        user_workspace = user_workspace_result.scalar_one_or_none()
        
        if not user_workspace:
            raise HTTPException(status_code=404, detail="Aucun espace de travail trouvé pour cet utilisateur")

        workspace_id = user_workspace.workspace_id
        print(f"📊 Workspace ID: {workspace_id}")

        # Construire la requête de base avec relations
        query = select(Lot).options(
            selectinload(Lot.project),
            selectinload(Lot.stakeholders).selectinload(Stakeholder.company)
        ).where(Lot.workspace_id == workspace_id)

        # Appliquer les filtres
        if search:
            search_filter = or_(
                Lot.name.ilike(f"%{search}%"),
                Lot.code.ilike(f"%{search}%"),
                Lot.description.ilike(f"%{search}%")
            )
            query = query.where(search_filter)

        if project_id:
            query = query.where(Lot.project_id == project_id)

        if phase:
            query = query.where(Lot.current_phase == phase)

        if is_active is not None:
            query = query.where(Lot.is_active == is_active)

        # Appliquer la pagination
        query = query.offset(skip).limit(limit).order_by(Lot.name)

        # Exécuter la requête
        result = await db.execute(query)
        lots = result.scalars().all()

        print(f"✅ {len(lots)} lots trouvés")
        return lots

    except Exception as e:
        print(f"❌ Erreur GET /lots: {e}")
        raise HTTPException(status_code=500, detail=f"Erreur lors de la récupération des lots: {str(e)}")


@router.get("/{lot_id}", response_model=LotResponse)
async def get_lot(
    lot_id: int,
    db: AsyncSession = Depends(deps.get_db),
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """Récupérer un lot par son ID"""
    try:
        user_id = current_user.get("id") or current_user.get("user_id")
        if not user_id:
            raise HTTPException(status_code=400, detail="Token utilisateur invalide")

        # Récupérer l'espace de travail de l'utilisateur
        user_workspace_result = await db.execute(
            select(UserWorkspace).where(
                and_(
                    UserWorkspace.user_id == user_id,
                    UserWorkspace.is_active == True
                )
            ).limit(1)
        )
        user_workspace = user_workspace_result.scalar_one_or_none()
        
        if not user_workspace:
            raise HTTPException(status_code=404, detail="Aucun espace de travail trouvé pour cet utilisateur")

        workspace_id = user_workspace.workspace_id

        # Récupérer le lot avec ses relations
        result = await db.execute(
            select(Lot).options(
                selectinload(Lot.project),
                selectinload(Lot.stakeholders).selectinload(Stakeholder.company),
                selectinload(Lot.documents).selectinload(LotDocument.document)
            ).where(
                and_(
                    Lot.id == lot_id,
                    Lot.workspace_id == workspace_id
                )
            )
        )
        lot = result.scalar_one_or_none()

        if not lot:
            raise HTTPException(status_code=404, detail="Lot non trouvé")

        return lot

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Erreur GET /lots/{lot_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Erreur lors de la récupération du lot: {str(e)}")


@router.post("/", response_model=LotResponse, status_code=status.HTTP_201_CREATED)
async def create_lot(
    *,
    db: AsyncSession = Depends(deps.get_db),
    lot_in: LotCreate,
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """Créer un nouveau lot"""
    try:
        print(f"🔍 POST /lots - Data received: {lot_in.dict()}")

        user_id = current_user.get("id") or current_user.get("user_id")
        if not user_id:
            raise HTTPException(status_code=400, detail="Token utilisateur invalide")

        # Récupérer l'espace de travail de l'utilisateur
        user_workspace_result = await db.execute(
            select(UserWorkspace).where(
                and_(
                    UserWorkspace.user_id == user_id,
                    UserWorkspace.is_active == True
                )
            ).limit(1)
        )
        user_workspace = user_workspace_result.scalar_one_or_none()
        
        if not user_workspace:
            raise HTTPException(status_code=404, detail="Aucun espace de travail trouvé pour cet utilisateur")

        workspace_id = user_workspace.workspace_id

        # Vérifier que le projet existe et appartient au workspace
        project_result = await db.execute(
            select(Project).where(
                and_(
                    Project.id == lot_in.project_id,
                    Project.workspace_id == workspace_id
                )
            )
        )
        project = project_result.scalar_one_or_none()
        
        if not project:
            raise HTTPException(status_code=404, detail="Projet non trouvé")

        # Vérifier l'unicité du code dans le projet
        if lot_in.code:
            existing_result = await db.execute(
                select(Lot).where(
                    and_(
                        Lot.code == lot_in.code,
                        Lot.project_id == lot_in.project_id
                    )
                )
            )
            existing_lot = existing_result.scalar_one_or_none()
            
            if existing_lot:
                raise HTTPException(status_code=400, detail="Un lot avec ce code existe déjà dans ce projet")

        # Créer le nouveau lot
        lot_data = lot_in.dict()
        lot_data.update({
            "workspace_id": workspace_id,
            "created_by": user_id,
            "current_phase": LotPhase.ESQ  # Phase initiale
        })
        
        db_lot = Lot(**lot_data)
        
        db.add(db_lot)
        await db.commit()
        
        # Récupérer le lot créé avec ses relations pour éviter DetachedInstanceError
        result = await db.execute(
            select(Lot).options(
                selectinload(Lot.project),
                selectinload(Lot.stakeholders).selectinload(Stakeholder.company),
                selectinload(Lot.documents).selectinload(LotDocument.document)
            ).where(Lot.id == db_lot.id)
        )
        created_lot = result.scalar_one()

        print(f"✅ Lot créé avec l'ID: {created_lot.id}")
        return created_lot

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Erreur POST /lots: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Erreur lors de la création du lot: {str(e)}")


@router.put("/{lot_id}", response_model=LotResponse)
async def update_lot(
    *,
    lot_id: int,
    db: AsyncSession = Depends(deps.get_db),
    lot_in: LotUpdate,
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """Mettre à jour un lot"""
    try:
        user_id = current_user.get("id") or current_user.get("user_id")
        if not user_id:
            raise HTTPException(status_code=400, detail="Token utilisateur invalide")

        # Récupérer l'espace de travail de l'utilisateur
        user_workspace_result = await db.execute(
            select(UserWorkspace).where(
                and_(
                    UserWorkspace.user_id == user_id,
                    UserWorkspace.is_active == True
                )
            ).limit(1)
        )
        user_workspace = user_workspace_result.scalar_one_or_none()
        
        if not user_workspace:
            raise HTTPException(status_code=404, detail="Aucun espace de travail trouvé pour cet utilisateur")

        workspace_id = user_workspace.workspace_id

        # Récupérer le lot existant
        result = await db.execute(
            select(Lot).where(
                and_(
                    Lot.id == lot_id,
                    Lot.workspace_id == workspace_id
                )
            )
        )
        db_lot = result.scalar_one_or_none()

        if not db_lot:
            raise HTTPException(status_code=404, detail="Lot non trouvé")

        # Mettre à jour les champs
        update_data = lot_in.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_lot, field, value)

        db_lot.updated_at = datetime.utcnow()
        await db.commit()
        
        # Récupérer le lot mis à jour avec ses relations pour éviter DetachedInstanceError
        result = await db.execute(
            select(Lot).options(
                selectinload(Lot.project),
                selectinload(Lot.stakeholders).selectinload(Stakeholder.company),
                selectinload(Lot.documents).selectinload(LotDocument.document)
            ).where(Lot.id == lot_id)
        )
        updated_lot = result.scalar_one()

        print(f"✅ Lot {lot_id} mis à jour")
        return updated_lot

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Erreur PUT /lots/{lot_id}: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Erreur lors de la mise à jour du lot: {str(e)}")


@router.delete("/{lot_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_lot(
    *,
    lot_id: int,
    db: AsyncSession = Depends(deps.get_db),
    current_user: Dict[str, Any] = Depends(require_auth),
):
    """Supprimer un lot (soft delete)"""
    try:
        user_id = current_user.get("id") or current_user.get("user_id")
        if not user_id:
            raise HTTPException(status_code=400, detail="Token utilisateur invalide")

        # Récupérer l'espace de travail de l'utilisateur
        user_workspace_result = await db.execute(
            select(UserWorkspace).where(
                and_(
                    UserWorkspace.user_id == user_id,
                    UserWorkspace.is_active == True
                )
            ).limit(1)
        )
        user_workspace = user_workspace_result.scalar_one_or_none()
        
        if not user_workspace:
            raise HTTPException(status_code=404, detail="Aucun espace de travail trouvé pour cet utilisateur")

        workspace_id = user_workspace.workspace_id

        # Récupérer le lot
        result = await db.execute(
            select(Lot).where(
                and_(
                    Lot.id == lot_id,
                    Lot.workspace_id == workspace_id
                )
            )
        )
        db_lot = result.scalar_one_or_none()

        if not db_lot:
            raise HTTPException(status_code=404, detail="Lot non trouvé")

        # Soft delete
        db_lot.is_active = False
        await db.commit()

        print(f"✅ Lot {lot_id} supprimé (soft delete)")

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Erreur DELETE /lots/{lot_id}: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Erreur lors de la suppression du lot: {str(e)}")


@router.get("/project/{project_id}", response_model=List[LotResponse])
async def get_lots_by_project(
    project_id: int,
    db: AsyncSession = Depends(deps.get_db),
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """Récupérer tous les lots d'un projet"""
    try:
        user_id = current_user.get("id") or current_user.get("user_id")
        if not user_id:
            raise HTTPException(status_code=400, detail="Token utilisateur invalide")

        # Récupérer l'espace de travail de l'utilisateur
        user_workspace_result = await db.execute(
            select(UserWorkspace).where(
                and_(
                    UserWorkspace.user_id == user_id,
                    UserWorkspace.is_active == True
                )
            ).limit(1)
        )
        user_workspace = user_workspace_result.scalar_one_or_none()
        
        if not user_workspace:
            raise HTTPException(status_code=404, detail="Aucun espace de travail trouvé pour cet utilisateur")

        workspace_id = user_workspace.workspace_id

        # Récupérer les lots du projet
        result = await db.execute(
            select(Lot).options(
                selectinload(Lot.project),
                selectinload(Lot.stakeholders).selectinload(Stakeholder.company),
                selectinload(Lot.documents).selectinload(LotDocument.document)
            ).where(
                and_(
                    Lot.project_id == project_id,
                    Lot.workspace_id == workspace_id,
                    Lot.is_active == True
                )
            ).order_by(Lot.name)
        )
        lots = result.scalars().all()

        return lots

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Erreur GET /lots/project/{project_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Erreur lors de la récupération des lots: {str(e)}")


@router.get("/phase/{phase}", response_model=List[LotResponse])
async def get_lots_by_phase(
    phase: LotPhase,
    db: AsyncSession = Depends(deps.get_db),
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """Récupérer tous les lots d'une phase donnée"""
    try:
        user_id = current_user.get("id") or current_user.get("user_id")
        if not user_id:
            raise HTTPException(status_code=400, detail="Token utilisateur invalide")

        # Récupérer l'espace de travail de l'utilisateur
        user_workspace_result = await db.execute(
            select(UserWorkspace).where(
                and_(
                    UserWorkspace.user_id == user_id,
                    UserWorkspace.is_active == True
                )
            ).limit(1)
        )
        user_workspace = user_workspace_result.scalar_one_or_none()
        
        if not user_workspace:
            raise HTTPException(status_code=404, detail="Aucun espace de travail trouvé pour cet utilisateur")

        workspace_id = user_workspace.workspace_id

        # Récupérer les lots de la phase
        result = await db.execute(
            select(Lot).options(
                selectinload(Lot.project),
                selectinload(Lot.stakeholders).selectinload(Stakeholder.company)
            ).where(
                and_(
                    Lot.current_phase == phase,
                    Lot.workspace_id == workspace_id,
                    Lot.is_active == True
                )
            ).order_by(Lot.name)
        )
        lots = result.scalars().all()

        return lots

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Erreur GET /lots/phase/{phase}: {e}")
        raise HTTPException(status_code=500, detail=f"Erreur lors de la récupération des lots: {str(e)}")


@router.get("/stats", response_model=LotStats)
async def get_lot_stats(
    db: AsyncSession = Depends(deps.get_db),
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """Récupérer les statistiques des lots"""
    try:
        user_id = current_user.get("id") or current_user.get("user_id")
        if not user_id:
            raise HTTPException(status_code=400, detail="Token utilisateur invalide")

        # Récupérer l'espace de travail de l'utilisateur
        user_workspace_result = await db.execute(
            select(UserWorkspace).where(
                and_(
                    UserWorkspace.user_id == user_id,
                    UserWorkspace.is_active == True
                )
            ).limit(1)
        )
        user_workspace = user_workspace_result.scalar_one_or_none()
        
        if not user_workspace:
            raise HTTPException(status_code=404, detail="Aucun espace de travail trouvé pour cet utilisateur")

        workspace_id = user_workspace.workspace_id

        # Statistiques par phase
        stats_by_phase = {}
        for phase in LotPhase:
            count_result = await db.execute(
                select(func.count(Lot.id)).where(
                    and_(
                        Lot.current_phase == phase,
                        Lot.workspace_id == workspace_id,
                        Lot.is_active == True
                    )
                )
            )
            stats_by_phase[phase.value] = count_result.scalar() or 0

        # Total des lots actifs
        total_result = await db.execute(
            select(func.count(Lot.id)).where(
                and_(
                    Lot.workspace_id == workspace_id,
                    Lot.is_active == True
                )
            )
        )
        total_active = total_result.scalar() or 0

        # Total des lots inactifs
        inactive_result = await db.execute(
            select(func.count(Lot.id)).where(
                and_(
                    Lot.workspace_id == workspace_id,
                    Lot.is_active == False
                )
            )
        )
        total_inactive = inactive_result.scalar() or 0

        return LotStats(
            total_active=total_active,
            total_inactive=total_inactive,
            by_phase=stats_by_phase
        )

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Erreur GET /lots/stats: {e}")
        raise HTTPException(status_code=500, detail=f"Erreur lors de la récupération des statistiques: {str(e)}")


@router.put("/{lot_id}/validate-phase", response_model=LotResponse)
async def validate_lot_phase(
    *,
    lot_id: int,
    phase_data: LotPhaseValidation,
    db: AsyncSession = Depends(deps.get_db),
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """Valider ou invalider une phase d'un lot"""
    try:
        user_id = current_user.get("id") or current_user.get("user_id")
        if not user_id:
            raise HTTPException(status_code=400, detail="Token utilisateur invalide")

        # Récupérer l'espace de travail de l'utilisateur
        user_workspace_result = await db.execute(
            select(UserWorkspace).where(
                and_(
                    UserWorkspace.user_id == user_id,
                    UserWorkspace.is_active == True
                )
            ).limit(1)
        )
        user_workspace = user_workspace_result.scalar_one_or_none()
        
        if not user_workspace:
            raise HTTPException(status_code=404, detail="Aucun espace de travail trouvé pour cet utilisateur")

        workspace_id = user_workspace.workspace_id

        # Récupérer le lot
        result = await db.execute(
            select(Lot).where(
                and_(
                    Lot.id == lot_id,
                    Lot.workspace_id == workspace_id
                )
            )
        )
        db_lot = result.scalar_one_or_none()

        if not db_lot:
            raise HTTPException(status_code=404, detail="Lot non trouvé")

        # Logique de validation des phases
        phase = phase_data.phase
        validated = phase_data.validated
        now = datetime.utcnow()

        if phase == LotPhase.ESQ:
            db_lot.esq_validated = validated
            db_lot.esq_validated_at = now if validated else None
            db_lot.esq_validated_by = user_id if validated else None
            if validated:
                db_lot.current_phase = LotPhase.APD
        elif phase == LotPhase.APD:
            if validated and not db_lot.esq_validated:
                raise HTTPException(status_code=400, detail="La phase ESQ doit être validée avant APD")
            db_lot.apd_validated = validated
            db_lot.apd_validated_at = now if validated else None
            db_lot.apd_validated_by = user_id if validated else None
            if validated:
                db_lot.current_phase = LotPhase.PRODCE
            else:
                db_lot.current_phase = LotPhase.ESQ
        elif phase == LotPhase.PRODCE:
            if validated and not db_lot.apd_validated:
                raise HTTPException(status_code=400, detail="La phase APD doit être validée avant PRODCE")
            db_lot.prodce_validated = validated
            db_lot.prodce_validated_at = now if validated else None
            db_lot.prodce_validated_by = user_id if validated else None
            if validated:
                db_lot.current_phase = LotPhase.EXE
            else:
                db_lot.current_phase = LotPhase.APD
        elif phase == LotPhase.EXE:
            if validated and not db_lot.prodce_validated:
                raise HTTPException(status_code=400, detail="La phase PRODCE doit être validée avant EXE")
            db_lot.exe_validated = validated
            db_lot.exe_validated_at = now if validated else None
            db_lot.exe_validated_by = user_id if validated else None
            if not validated:
                db_lot.current_phase = LotPhase.PRODCE

        await db.commit()
        
        # Récupérer le lot mis à jour avec ses relations pour éviter DetachedInstanceError
        result = await db.execute(
            select(Lot).options(
                selectinload(Lot.project),
                selectinload(Lot.stakeholders).selectinload(Stakeholder.company),
                selectinload(Lot.documents).selectinload(LotDocument.document)
            ).where(Lot.id == lot_id)
        )
        updated_lot = result.scalar_one()

        print(f"✅ Phase {phase.value} du lot {lot_id} {'validée' if validated else 'invalidée'}")
        return updated_lot

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Erreur PUT /lots/{lot_id}/validate-phase: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Erreur lors de la validation de la phase: {str(e)}")
