// Types pour les documents techniques (CCTP/DPGF)

export enum DocumentType {
  CCTP = 'CCTP',
  DPGF = 'DPGF'
}

export interface UserSimple {
  id: number
  email: string
  first_name?: string
  last_name?: string
}

export interface CompanySimple {
  id: number
  name: string
  code: string
}

export interface TCompany {
  id: number
  company_name: string
  activity?: string
  address?: string
  postal_code?: string
  city?: string
  country?: string
  phone?: string
  fax?: string
  email?: string
  siret?: string
  vat_number?: string
  legal_representative_id?: number
  workspace_id: number
  is_active: boolean
  created_at: string
  updated_at: string
  created_by?: number
  legal_representative_name?: string
  legal_representative_email?: string
}

export interface ProjectSimple {
  id: number
  name: string
  code?: string
}

export interface LotSimple {
  id: number
  name: string
  code?: string
  project?: ProjectSimple
}

export interface TechnicalDocumentBase {
  name?: string
  type_document?: DocumentType
  content?: string
  lot_id?: number
}

export interface TechnicalDocumentCreate extends TechnicalDocumentBase {
  name: string
  type_document: DocumentType
  lot_id: number
  company_ids?: number[]
}

export interface TechnicalDocumentUpdate extends TechnicalDocumentBase {
  company_ids?: number[]
}

export interface TechnicalDocumentResponse extends TechnicalDocumentBase {
  id: number
  created_by?: number
  updated_by?: number
  is_active?: boolean
  created_at?: string
  updated_at?: string
  lot?: LotSimple
  creator?: UserSimple
  updater?: UserSimple
  companies: CompanySimple[]
}

export interface TechnicalDocumentList extends TechnicalDocumentBase {
  id: number
  created_by?: number
  updated_by?: number
  is_active?: boolean
  created_at?: string
  updated_at?: string
  lot?: LotSimple
  creator?: UserSimple
  company_count?: number
}

export interface TechnicalDocumentCompanyCreate {
  technical_document_id: number
  company_id: number
}

export interface TechnicalDocumentCompanyResponse extends TechnicalDocumentCompanyCreate {
  id: number
  created_at: string
}

export interface TextEnhancementRequest {
  text: string
  prompt_type: string
  document_type: DocumentType
  context?: string
}

export interface TextEnhancementResponse {
  original_text: string
  enhanced_text: string
  prompt_type: string
  document_type: DocumentType
  processing_time?: number
}

export interface TechnicalDocumentFilter {
  project_id?: number
  lot_id?: number
  type_document?: DocumentType
  company_id?: number
  created_by?: number
  is_active?: boolean
  search_term?: string
}

export interface TechnicalDocumentSearchResponse {
  documents: TechnicalDocumentList[]
  total_count: number
  page: number
  page_size: number
  total_pages: number
}

export interface TechnicalDocumentStats {
  total_documents: number
  cctp_count: number
  dpgf_count: number
  active_documents: number
  documents_by_project: Record<string, number>
}

// Types pour les prompts d'amélioration
export type PromptType = 'enhance' | 'rephrase' | 'expand' | 'simplify'

export interface PromptTypeInfo {
  key: PromptType
  label: string
  description: string
  icon: string
}

export const PROMPT_TYPES: PromptTypeInfo[] = [
  {
    key: 'enhance',
    label: 'Améliorer',
    description: 'Améliorer la qualité technique et la précision',
    icon: '✨'
  },
  {
    key: 'rephrase',
    label: 'Reformuler',
    description: 'Reformuler pour plus de clarté',
    icon: '🔄'
  },
  {
    key: 'expand',
    label: 'Développer',
    description: 'Ajouter des détails techniques',
    icon: '📝'
  },
  {
    key: 'simplify',
    label: 'Simplifier',
    description: 'Simplifier tout en gardant l\'essentiel',
    icon: '🎯'
  }
]

// Types pour les erreurs
export interface TechnicalDocumentError {
  message: string
  field?: string
  code?: string
}

// Types pour les hooks
export interface UseTechnicalDocumentReturn {
  document: TechnicalDocumentResponse | null
  loading: boolean
  error: string | null
  save: (data: Partial<TechnicalDocumentUpdate>) => Promise<void>
  refresh: () => Promise<void>
}

export interface UseTechnicalDocumentListReturn {
  documents: TechnicalDocumentList[]
  loading: boolean
  error: string | null
  total: number
  page: number
  pageSize: number
  filters: TechnicalDocumentFilter
  setFilters: (filters: Partial<TechnicalDocumentFilter>) => void
  setPage: (page: number) => void
  refresh: () => Promise<void>
}

export interface UseTextEnhancementReturn {
  enhanceText: (request: TextEnhancementRequest) => Promise<TextEnhancementResponse>
  loading: boolean
  error: string | null
}

// Types pour les composants
export interface DocumentEditorProps {
  document?: TechnicalDocumentResponse
  onSave: (content: string) => Promise<void>
  onCompaniesChange: (companyIds: number[]) => Promise<void>
  readOnly?: boolean
}

export interface CompanySelectorProps {
  selectedCompanies: CompanySimple[]
  availableCompanies: CompanySimple[]
  onSelectionChange: (companies: CompanySimple[]) => void
  loading?: boolean
  error?: string | null
}

export interface ContextMenuProps {
  visible: boolean
  position: { x: number; y: number }
  selectedText: string
  documentType: DocumentType
  onAction: (promptType: PromptType) => void
  onClose: () => void
}

export interface DocumentManagerProps {
  projectId: number
  onDocumentSelect: (document: TechnicalDocumentResponse) => void
  onDocumentCreate: (type: DocumentType) => void
}

// Types pour l'API
export interface ApiResponse<T> {
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  page_size: number
  total_pages: number
}

// Configuration de l'éditeur
export interface EditorConfig {
  height: number
  plugins: string[]
  toolbar: string
  documentType: DocumentType
}

// Types pour les templates
export interface DocumentTemplate {
  id: string
  name: string
  description: string
  content: string
  documentType: DocumentType
  category: string
}

export const DOCUMENT_TEMPLATES: DocumentTemplate[] = [
  {
    id: 'cctp-article',
    name: 'Article CCTP standard',
    description: 'Structure d\'article CCTP avec spécifications techniques',
    documentType: DocumentType.CCTP,
    category: 'Structure',
    content: `
      <h3>Article X.X - [Titre de l'article]</h3>
      <div class="technical-spec">
        <h4>Spécifications techniques</h4>
        <p>[Détails techniques]</p>
      </div>
      <h4>Mise en œuvre</h4>
      <p>[Conditions d'exécution]</p>
      <h4>Contrôles et réception</h4>
      <p>[Modalités de contrôle]</p>
    `
  },
  {
    id: 'dpgf-poste',
    name: 'Poste DPGF standard',
    description: 'Structure de poste DPGF avec tableau de quantités',
    documentType: DocumentType.DPGF,
    category: 'Structure',
    content: `
      <h3>[Code poste] - [Désignation]</h3>
      <table>
        <tr><th>Désignation</th><th>Unité</th><th>Quantité</th><th>Prix unitaire</th><th>Prix total</th></tr>
        <tr><td>[Description détaillée]</td><td>[m², m³, ml, u, etc.]</td><td>[Quantité]</td><td>[Prix HT]</td><td>[Total HT]</td></tr>
      </table>
    `
  }
]
