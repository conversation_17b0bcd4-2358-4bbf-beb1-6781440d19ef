'use client'

import React, { useState, useRef } from 'react'
import { PhotoIcon, TrashIcon } from '@heroicons/react/24/outline'
import Image from 'next/image'

interface LogoUploadProps {
  currentLogoUrl?: string | null
  onUpload: (file: File) => Promise<void>
  onDelete?: () => Promise<void>
  loading?: boolean
  disabled?: boolean
  className?: string
}

export default function LogoUpload({
  currentLogoUrl,
  onUpload,
  onDelete,
  loading = false,
  disabled = false,
  className = ''
}: LogoUploadProps) {
  const [dragOver, setDragOver] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = async (file: File) => {
    // Vérifier le type de fichier
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      alert('Type de fichier non autorisé. Utilisez: JPG, PNG, GIF ou WebP')
      return
    }

    // Vérifier la taille (max 5MB)
    const maxSize = 5 * 1024 * 1024 // 5MB
    if (file.size > maxSize) {
      alert('Le fichier est trop volumineux (max 5MB)')
      return
    }

    try {
      await onUpload(file)
    } catch (error) {
      console.error('Erreur lors de l\'upload:', error)
      alert('Erreur lors de l\'upload du logo')
    }
  }

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      handleFileSelect(file)
    }
    // Reset input value pour permettre de sélectionner le même fichier
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault()
    setDragOver(false)
    
    const file = event.dataTransfer.files[0]
    if (file) {
      handleFileSelect(file)
    }
  }

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault()
    setDragOver(true)
  }

  const handleDragLeave = () => {
    setDragOver(false)
  }

  const handleClick = () => {
    if (!disabled && !loading && fileInputRef.current) {
      fileInputRef.current.click()
    }
  }

  const handleDelete = async () => {
    if (onDelete && confirm('Êtes-vous sûr de vouloir supprimer ce logo ?')) {
      try {
        await onDelete()
      } catch (error) {
        console.error('Erreur lors de la suppression:', error)
        alert('Erreur lors de la suppression du logo')
      }
    }
  }

  const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'

  return (
    <div className={`space-y-4 ${className}`}>
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileChange}
        className="hidden"
        disabled={disabled || loading}
      />

      {/* Zone d'affichage du logo actuel */}
      {currentLogoUrl && (
        <div className="relative inline-block">
          <div className="w-32 h-32 border-2 border-gray-200 rounded-lg overflow-hidden bg-white">
            <Image
              src={`${API_BASE_URL}${currentLogoUrl}`}
              alt="Logo de l'entreprise"
              width={128}
              height={128}
              className="w-full h-full object-contain"
              onError={(e) => {
                console.error('Erreur de chargement du logo:', currentLogoUrl)
                // Fallback en cas d'erreur
                e.currentTarget.style.display = 'none'
              }}
            />
          </div>
          
          {/* Bouton de suppression */}
          {onDelete && (
            <button
              onClick={handleDelete}
              disabled={loading || disabled}
              className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 disabled:opacity-50"
              title="Supprimer le logo"
            >
              <TrashIcon className="h-4 w-4" />
            </button>
          )}
        </div>
      )}

      {/* Zone de drop/upload */}
      <div
        onClick={handleClick}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        className={`
          border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors
          ${dragOver ? 'border-blue-400 bg-blue-50' : 'border-gray-300 hover:border-gray-400'}
          ${disabled || loading ? 'opacity-50 cursor-not-allowed' : ''}
          ${currentLogoUrl ? 'border-gray-200' : ''}
        `}
      >
        <PhotoIcon className="mx-auto h-12 w-12 text-gray-400" />
        <div className="mt-2">
          <p className="text-sm text-gray-600">
            {loading ? 'Upload en cours...' : 'Cliquez ou glissez-déposez un logo'}
          </p>
          <p className="text-xs text-gray-500 mt-1">
            PNG, JPG, GIF ou WebP (max 5MB)
          </p>
        </div>
      </div>

      {loading && (
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-sm text-gray-600">Upload en cours...</span>
        </div>
      )}
    </div>
  )
}
