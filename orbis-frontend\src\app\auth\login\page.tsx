'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { useAuth } from '@/contexts/AuthContext'
import { FastAuthService } from '@/lib/auth'
// import { Building2, Mail, Lock, Eye, EyeOff, AlertCircle } from 'lucide-react'

export default function LoginPage() {
  const [email, setEmail] = useState('<EMAIL>')
  const [password, setPassword] = useState('orbis123!')
  const [showPassword, setShowPassword] = useState(false)
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(false)

  const router = useRouter()
  const { user, signIn } = useAuth()

  // Nettoyer localStorage seulement si les données sont corrompues (une seule fois)
  useEffect(() => {
    // Seulement si on n'est pas déjà en train de charger depuis AuthContext
    if (!user) {
      try {
        const token = FastAuthService.getToken()
        const userData = FastAuthService.getUser()

        // Seulement nettoyer si les données sont corrompues ou incomplètes
        if ((token && !userData) || (!token && userData)) {
          console.log('🧹 LoginPage - Nettoyage données corrompues...')
          FastAuthService.logout()
        }
      } catch (error) {
        console.log('🧹 LoginPage - Nettoyage après erreur...')
        FastAuthService.logout()
      }
    }
  }, [])

  // Rediriger si déjà connecté
  useEffect(() => {
    console.log('🔍 LoginPage - Vérification auth:', {
      user: user?.email,
      isAuthenticated: !!user
    })

    if (user) {
      console.log('🔄 LoginPage - Redirection vers /dashboard (déjà connecté)')
      router.push('/dashboard')
    }
  }, [user, router])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setLoading(true)

    try {
      console.log('🚀 Connexion pour:', email)
      await signIn(email, password)
      console.log('✅ Connexion réussie, redirection immédiate...')

      // La redirection sera gérée par le useEffect qui surveille l'état user
      // Pas besoin de router.push ici
    } catch (error: any) {
      console.error('❌ Erreur de connexion:', error)

      let errorMessage = 'Erreur de connexion. Veuillez réessayer.'

      if (error.message?.includes('Invalid credentials')) {
        errorMessage = 'Email ou mot de passe incorrect'
      } else if (error.message?.includes('Failed to fetch')) {
        errorMessage = 'Erreur de connexion au serveur. Vérifiez votre connexion internet.'
      } else if (error.message?.includes('Network')) {
        errorMessage = 'Erreur réseau. Vérifiez votre connexion.'
      } else if (error.message) {
        errorMessage = `Erreur: ${error.message}`
      }

      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-cyan-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="bg-white rounded-xl shadow-lg p-8">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="flex justify-center mb-4">
              <div className="bg-gradient-to-r from-primary-600 to-cyan-600 rounded-full p-3 shadow-lg">
                <span className="text-2xl text-white">🏗️</span>
              </div>
            </div>
            <h2 className="text-3xl font-bold text-gray-900">ORBIS Frontend</h2>
            <p className="mt-2 text-gray-600">
              Connectez-vous à votre espace de gestion BTP
            </p>
          </div>

          {/* Formulaire de connexion */}
          <form className="space-y-6" onSubmit={handleSubmit}>
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-4 rounded-md shadow-sm">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <span className="text-red-400">⚠️</span>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium">{error}</p>
                  </div>
                </div>
              </div>
            )}

            <div className="space-y-6">
              {/* Email */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  📧 Adresse email
                </label>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                  placeholder="<EMAIL>"
                  required
                />
              </div>

              {/* Mot de passe */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  🔒 Mot de passe
                </label>
                <div className="relative">
                  <input
                    type={showPassword ? 'text' : 'password'}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                    placeholder="Votre mot de passe"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                  >
                    {showPassword ? '🙈' : '👁️'}
                  </button>
                </div>
              </div>
            </div>

            <button
              type="submit"
              disabled={loading}
              className="w-full bg-gradient-to-r from-primary-600 to-cyan-600 text-white py-3 px-6 rounded-lg font-semibold hover:from-primary-700 hover:to-cyan-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              {loading ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  Connexion...
                </div>
              ) : (
                'Se connecter'
              )}
            </button>

            <div className="text-center">
              <span className="text-sm text-gray-600">
                Pas encore de compte ?{' '}
                <Link href="/auth/register" className="font-semibold text-primary-600 hover:text-primary-700 transition-colors">
                  Créer un compte
                </Link>
              </span>
            </div>
          </form>
        </div>

        {/* Footer */}
        <div className="text-center mt-8">
          <div className="flex items-center justify-center space-x-4 text-xs text-gray-400">
            <div className="flex items-center">
              <div className="w-2 h-2 bg-primary-400 rounded-full mr-2"></div>
              Backend API
            </div>
            <div className="flex items-center">
              <div className="w-2 h-2 bg-primary-400 rounded-full mr-2"></div>
              Frontend
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}