// Service pour la gestion des rôles métier
import { FastAuthService } from './FastAuthService';

export interface BusinessRole {
  id: number;
  name: string;
  description: string;
}

export class RoleService {
  private static baseUrl = 'http://localhost:8000/api/v1';

  /**
   * Récupérer tous les rôles métier (à partir de l'ID 6)
   */
  static async getBusinessRoles(): Promise<BusinessRole[]> {
    try {
      const response = await FastAuthService.makeAuthenticatedRequest(
        `${this.baseUrl}/stakeholders/roles`,
        {
          method: 'GET',
        }
      );

      if (!response.ok) {
        throw new Error(`Erreur ${response.status}: ${response.statusText}`);
      }

      const roles = await response.json();
      console.log('🎭 Rôles métier récupérés:', roles);
      
      return roles;
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des rôles métier:', error);
      throw error;
    }
  }

  /**
   * Obtenir la description d'un rôle par son nom
   */
  static getRoleDescription(roleName: string): string {
    const roleDescriptions: Record<string, string> = {
      'MOA': 'Maître d\'Ouvrage',
      'MOADEL': 'Maître d\'Ouvrage Délégué',
      'ARCHI': 'Architecte',
      'BE': 'Bureau d\'Études',
      'BC': 'Bureau de Contrôle',
      'OPC': 'Ordonnancement Pilotage Coordination',
      'ENT': 'Entreprise',
      'FO': 'Fournisseur'
    };

    return roleDescriptions[roleName] || roleName;
  }

  /**
   * Vérifier si un rôle est un rôle de maîtrise d'œuvre
   */
  static isMaitriseOeuvre(roleName: string): boolean {
    return ['ARCHI', 'BE'].includes(roleName);
  }

  /**
   * Vérifier si un rôle est un rôle de maître d'ouvrage
   */
  static isMaitreOuvrage(roleName: string): boolean {
    return ['MOA', 'MOADEL'].includes(roleName);
  }
}
