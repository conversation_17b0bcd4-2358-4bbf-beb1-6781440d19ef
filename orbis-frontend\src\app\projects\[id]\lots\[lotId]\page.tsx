'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { BusinessDataService } from '@/lib/auth'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Modal } from '@/components/ui/Modal'
import { Input } from '@/components/ui/Input'
import ProtectedRoute from '@/components/ProtectedRoute'
import ModernSidebar from '@/components/ModernSidebar'
import ModernHeader from '@/components/ModernHeader'
import LotPhaseTracker from '@/components/lots/LotPhaseTracker'
import LotPhaseProgress from '@/components/lots/LotPhaseProgress'
import { StakeholdersList } from '@/components/stakeholders/StakeholdersList'
import DocumentManager from '@/components/technical-documents/DocumentManager'
import { lotsApiService } from '@/lib/api/lots'
import { Lot, PHASE_LABELS, PHASE_COLORS } from '@/types/lot'
import { DocumentType, TechnicalDocumentResponse } from '@/types/technical-document'
import Link from 'next/link'

export default function LotDetails() {
  const { user, signOut } = useAuth()
  const params = useParams()
  const router = useRouter()
  
  // États pour le lot et le projet
  const [lot, setLot] = useState<Lot | null>(null)
  const [project, setProject] = useState<Record<string, any> | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  // États pour les tabs
  const [activeTab, setActiveTab] = useState('intervenants')
  
  // États pour les modales
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)

  // Charger le lot et le projet
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)
        console.log('🔍 Fetching lot with ID:', params.lotId)
        console.log('🔍 Project ID:', params.id)
        
        const lotId = parseInt(params.lotId as string)
        const projectId = parseInt(params.id as string)
        
        // Charger le lot et le projet en parallèle
        const [lotData, projectData] = await Promise.all([
          lotsApiService.getLot(lotId),
          BusinessDataService.getProject(projectId)
        ])
        
        console.log('✅ Lot data received:', lotData)
        console.log('✅ Project data received:', projectData)
        
        setLot(lotData)
        setProject(projectData)
        setError(null)
      } catch (err) {
        console.error('❌ Error fetching data:', err)
        setError('Erreur lors du chargement des détails du lot')
      } finally {
        setLoading(false)
      }
    }

    if (params.lotId && params.id) {
      fetchData()
    }
  }, [params.lotId, params.id])

  // Fonction pour mettre à jour le lot après validation de phase
  const handleLotPhaseUpdate = (updatedLot: Lot) => {
    setLot(updatedLot)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="text-center">
          <div className="text-red-600 text-lg mb-4">{error}</div>
          <Button onClick={() => window.location.reload()}>
            Réessayer
          </Button>
        </div>
      </div>
    )
  }

  if (!lot || !project) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-gray-600">Lot ou projet non trouvé</div>
      </div>
    )
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        <div className="flex">
          <ModernSidebar user={user ? {
            name: `${user.first_name} ${user.last_name}`,
            email: user.email
          } : undefined} />

          <div className="flex-1 lg:ml-72">
            <ModernHeader
              title={`${lot.name} - ${project.name}`}
              subtitle={lot.description || 'Détails du lot'}
              user={user ? {
                name: `${user.first_name} ${user.last_name}`,
                email: user.email
              } : undefined}
              onLogout={signOut}
            />

            <main className="p-6">
              <div className="space-y-6">
                {/* Header du lot */}
                <div className="flex justify-between items-start">
                  <div>
                    <p className="text-sm text-gray-500">
                      Projet: <Link href={`/projects/${project.id}`} className="text-blue-600 hover:underline">{project.name}</Link>
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" onClick={() => setIsEditModalOpen(true)}>
                      Modifier
                    </Button>
                    <Link href={`/projects/${project.id}`}>
                      <Button variant="outline">Retour au projet</Button>
                    </Link>
                  </div>
                </div>

                {/* Chronologie horizontale des phases */}
                <Card className="p-6">
                  <h2 className="text-lg font-semibold mb-4">Progression des phases</h2>
                  <LotPhaseProgress 
                    lot={lot} 
                    onPhaseUpdate={handleLotPhaseUpdate}
                  />
                </Card>

                {/* Informations du lot */}
                <Card className="p-6">
                  <h2 className="text-lg font-semibold mb-4">Informations du lot</h2>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                      <span className="text-sm text-gray-600">Phase actuelle:</span>
                      <p className="font-medium">{PHASE_LABELS[lot.current_phase]}</p>
                    </div>
                    <div>
                      <span className="text-sm text-gray-600">Créé le:</span>
                      <p className="font-medium">
                        {new Date(lot.created_at).toLocaleDateString('fr-FR')}
                      </p>
                    </div>
                    <div>
                      <span className="text-sm text-gray-600">Dernière mise à jour:</span>
                      <p className="font-medium">
                        {new Date(lot.updated_at).toLocaleDateString('fr-FR')}
                      </p>
                    </div>
                  </div>
                </Card>

                {/* Tabs pour les détails */}
                <div className="border-b border-gray-200">
                  <nav className="-mb-px flex space-x-8">
                    {[
                      { id: 'intervenants', label: 'Intervenants' },
                      { id: 'pieces-generales', label: 'Pièces générales' },
                      { id: 'pieces-techniques', label: 'Pièces techniques' }
                    ].map((tab) => (
                      <button
                        key={tab.id}
                        onClick={() => setActiveTab(tab.id)}
                        className={`py-2 px-1 border-b-2 font-medium text-sm ${
                          activeTab === tab.id
                            ? 'border-blue-500 text-blue-600'
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        }`}
                      >
                        {tab.label}
                      </button>
                    ))}
                  </nav>
                </div>

                {/* Contenu des tabs */}

                {activeTab === 'intervenants' && (
                  <StakeholdersList lotId={lot.id} />
                )}

                {activeTab === 'pieces-generales' && (
                  <Card className="p-6">
                    <div className="flex justify-between items-center mb-4">
                      <h2 className="text-xl font-semibold">Pièces générales</h2>
                      <Button>
                        Ajouter un document
                      </Button>
                    </div>
                    
                    <div className="text-center py-12">
                      <div className="text-4xl mb-4">📄</div>
                      <p className="text-gray-500 text-lg mb-2">Aucune pièce générale</p>
                      <p className="text-sm text-gray-400 mb-4">
                        Les documents généraux du lot apparaîtront ici
                      </p>
                      <Button>
                        Ajouter le premier document
                      </Button>
                    </div>
                  </Card>
                )}

                {activeTab === 'pieces-techniques' && (
                  <DocumentManager
                    lotId={lot.id}
                    projectId={project.id}
                    onDocumentSelect={(document: TechnicalDocumentResponse) => {
                      // Naviguer vers l'éditeur de document avec les bons paramètres
                      router.push(`/documents-techniques?project_id=${project.id}&lot_id=${lot.id}&document_id=${document.id}`)
                    }}
                    onDocumentCreate={(type: DocumentType) => {
                      // Naviguer vers la création de document avec le type spécifié
                      router.push(`/documents-techniques?project_id=${project.id}&lot_id=${lot.id}&type=${type}`)
                    }}
                  />
                )}

                {/* Modal de modification du lot */}
                <Modal isOpen={isEditModalOpen} onClose={() => setIsEditModalOpen(false)}>
                  <div className="p-6">
                    <h2 className="text-xl font-semibold mb-4">Modifier le lot</h2>
                    <div className="space-y-4">
                      <Input label="Nom du lot" defaultValue={lot.name} />
                      <Input label="Code du lot" defaultValue={lot.code} />
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">
                          Description
                        </label>
                        <textarea
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          rows={3}
                          defaultValue={lot.description || ''}
                          placeholder="Description du lot..."
                        />
                      </div>
                      <div className="flex justify-end gap-2 mt-6">
                        <Button variant="outline" onClick={() => setIsEditModalOpen(false)}>
                          Annuler
                        </Button>
                        <Button onClick={() => setIsEditModalOpen(false)}>
                          Enregistrer
                        </Button>
                      </div>
                    </div>
                  </div>
                </Modal>
              </div>
            </main>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  )
}
