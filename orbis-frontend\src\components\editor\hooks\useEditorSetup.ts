import { useEditor } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import { Underline } from '@tiptap/extension-underline'
import { TextAlign } from '@tiptap/extension-text-align'
import { Color } from '@tiptap/extension-color'
import { TextStyle } from '@tiptap/extension-text-style'
import { Highlight } from '@tiptap/extension-highlight'
import { Link } from '@tiptap/extension-link'
import { Table } from '@tiptap/extension-table'
import { TableRow } from '@tiptap/extension-table-row'
import { TableHeader } from '@tiptap/extension-table-header'
import { TableCell } from '@tiptap/extension-table-cell'
import { useRef } from 'react'

interface UseEditorSetupProps {
  value: string
  onChange: (content: string) => void
  onTextSelection?: (selectedText: string) => void
  readOnly?: boolean
}

export function useEditorSetup({ 
  value, 
  onChange, 
  onTextSelection, 
  readOnly = false 
}: UseEditorSetupProps) {
  const changeTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const autoSaveTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3, 4, 5, 6],
        },
      }),
      Underline,
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      Color,
      TextStyle,
      Highlight.configure({
        multicolor: true,
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-600 underline',
        },
      }),
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableHeader,
      TableCell,
    ],
    content: value,
    editable: !readOnly,
    immediatelyRender: false, // Fix pour SSR avec Next.js
    onUpdate: ({ editor }) => {
      const html = editor.getHTML()
      
      // Débounce pour éviter trop d'appels
      if (changeTimeoutRef.current) {
        clearTimeout(changeTimeoutRef.current)
      }
      
      changeTimeoutRef.current = setTimeout(() => {
        onChange(html)
      }, 300)
    },
    onSelectionUpdate: ({ editor }) => {
      if (onTextSelection) {
        const { from, to } = editor.state.selection
        const selectedText = editor.state.doc.textBetween(from, to)
        if (selectedText.trim()) {
          onTextSelection(selectedText)
        }
      }
    },
  })

  return {
    editor,
    changeTimeoutRef,
    autoSaveTimeoutRef
  }
}
