# app/api/api_v1/endpoints/tcompanies.py
"""
API endpoints pour les TCompanies (carnet d'adresses)
"""

from typing import List, Optional, Any, Dict
from fastapi import APIRouter, Depends, HTTPException, Query, status, UploadFile, File
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func
import os
import uuid
from pathlib import Path

from app.api import deps
from app.models.user import User
from app.models.workspace import UserWorkspace, Workspace
from app.models.tcompany import TCompany
from app.schemas.tcompany import (
    TCompanyCreate,
    TCompanyUpdate,
    TCompanyResponse,
    TCompanyList,
    TCompanyStats
)
from app.middleware.auth_sync_middleware import require_auth

router = APIRouter()


@router.get("/", response_model=List[TCompanyResponse])
async def get_tcompanies(
    db: AsyncSession = Depends(deps.get_db),
    skip: int = Query(0, ge=0, description="Nombre d'éléments à ignorer"),
    limit: int = Query(100, ge=1, le=1000, description="Nombre maximum d'éléments à retourner"),
    search: Optional[str] = Query(None, description="Recherche dans nom, email, SIRET, ville"),
    is_active: Optional[bool] = Query(None, description="Filtrer par statut actif/inactif"),
    activity: Optional[str] = Query(None, description="Filtrer par activité"),
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """Récupérer la liste des TCompanies avec pagination et filtres"""
    try:
        print(f"🔍 GET /tcompanies - User: {current_user}")
        
        user_id = current_user.get("user_id")
        if not user_id:
            raise HTTPException(status_code=400, detail="Token utilisateur invalide")

        # Récupérer l'espace de travail de l'utilisateur via UserWorkspace
        user_workspace_result = await db.execute(
            select(UserWorkspace).where(
                and_(
                    UserWorkspace.user_id == user_id,
                    UserWorkspace.is_active == True
                )
            ).limit(1)
        )
        user_workspace = user_workspace_result.scalar_one_or_none()
        
        if not user_workspace:
            raise HTTPException(status_code=404, detail="Aucun espace de travail trouvé pour cet utilisateur")

        workspace_id = user_workspace.workspace_id
        print(f"📊 Workspace ID: {workspace_id}")

        # Construire la requête de base
        query = select(TCompany).where(TCompany.workspace_id == workspace_id)

        # Appliquer les filtres
        if search:
            search_filter = or_(
                TCompany.company_name.ilike(f"%{search}%"),
                TCompany.email.ilike(f"%{search}%"),
                TCompany.siret.ilike(f"%{search}%"),
                TCompany.city.ilike(f"%{search}%")
            )
            query = query.where(search_filter)

        if is_active is not None:
            query = query.where(TCompany.is_active == is_active)

        if activity:
            query = query.where(TCompany.activity.ilike(f"%{activity}%"))

        # Appliquer la pagination
        query = query.offset(skip).limit(limit).order_by(TCompany.company_name)

        # Exécuter la requête
        result = await db.execute(query)
        tcompanies = result.scalars().all()

        print(f"✅ {len(tcompanies)} TCompanies trouvées")
        
        # Convertir en schémas Pydantic pour éviter les erreurs DetachedInstanceError
        return [TCompanyResponse.from_orm(tcompany) for tcompany in tcompanies]

    except Exception as e:
        print(f"❌ Erreur GET /tcompanies: {e}")
        raise HTTPException(status_code=500, detail=f"Erreur lors de la récupération des TCompanies: {str(e)}")


@router.get("/{tcompany_id}", response_model=TCompanyResponse)
async def get_tcompany(
    tcompany_id: int,
    db: AsyncSession = Depends(deps.get_db),
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """Récupérer une TCompany par son ID"""
    try:
        user_id = current_user.get("user_id")
        if not user_id:
            raise HTTPException(status_code=400, detail="Token utilisateur invalide")

        # Récupérer l'espace de travail de l'utilisateur via UserWorkspace
        user_workspace_result = await db.execute(
            select(UserWorkspace).where(
                and_(
                    UserWorkspace.user_id == user_id,
                    UserWorkspace.is_active == True
                )
            ).limit(1)
        )
        user_workspace = user_workspace_result.scalar_one_or_none()
        
        if not user_workspace:
            raise HTTPException(status_code=404, detail="Aucun espace de travail trouvé pour cet utilisateur")

        workspace_id = user_workspace.workspace_id

        # Récupérer la TCompany
        result = await db.execute(
            select(TCompany).where(
                and_(
                    TCompany.id == tcompany_id,
                    TCompany.workspace_id == workspace_id
                )
            )
        )
        tcompany = result.scalar_one_or_none()

        if not tcompany:
            raise HTTPException(status_code=404, detail="TCompany non trouvée")

        # Convertir en schéma Pydantic pour éviter les erreurs DetachedInstanceError
        return TCompanyResponse.from_orm(tcompany)

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Erreur GET /tcompanies/{tcompany_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Erreur lors de la récupération de la TCompany: {str(e)}")


@router.post("/", response_model=TCompanyResponse, status_code=status.HTTP_201_CREATED)
async def create_tcompany(
    *,
    db: AsyncSession = Depends(deps.get_db),
    tcompany_in: TCompanyCreate,
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """Créer une nouvelle TCompany"""
    try:
        print(f"🔍 POST /tcompanies - Data received: {tcompany_in.dict()}")

        user_id = current_user.get("user_id")
        if not user_id:
            raise HTTPException(status_code=400, detail="Token utilisateur invalide")

        # Récupérer l'espace de travail de l'utilisateur via UserWorkspace
        user_workspace_result = await db.execute(
            select(UserWorkspace).where(
                and_(
                    UserWorkspace.user_id == user_id,
                    UserWorkspace.is_active == True
                )
            ).limit(1)
        )
        user_workspace = user_workspace_result.scalar_one_or_none()
        
        if not user_workspace:
            raise HTTPException(status_code=404, detail="Aucun espace de travail trouvé pour cet utilisateur")

        workspace_id = user_workspace.workspace_id

        # Vérifier l'unicité du nom dans l'espace de travail
        existing_result = await db.execute(
            select(TCompany).where(
                and_(
                    TCompany.company_name == tcompany_in.company_name,
                    TCompany.workspace_id == workspace_id
                )
            )
        )
        existing_tcompany = existing_result.scalar_one_or_none()
        
        if existing_tcompany:
            raise HTTPException(status_code=400, detail="Une TCompany avec ce nom existe déjà dans votre espace de travail")

        # Créer la nouvelle TCompany
        db_tcompany = TCompany(
            **tcompany_in.dict(),
            workspace_id=workspace_id,
            created_by=user_id
        )
        
        db.add(db_tcompany)
        await db.commit()
        await db.refresh(db_tcompany)

        print(f"✅ TCompany créée avec l'ID: {db_tcompany.id}")
        
        # Convertir en schéma Pydantic pour éviter les erreurs DetachedInstanceError
        return TCompanyResponse.from_orm(db_tcompany)

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Erreur POST /tcompanies: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Erreur lors de la création de la TCompany: {str(e)}")


@router.put("/{tcompany_id}", response_model=TCompanyResponse)
async def update_tcompany(
    *,
    tcompany_id: int,
    db: AsyncSession = Depends(deps.get_db),
    tcompany_in: TCompanyUpdate,
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """Mettre à jour une TCompany"""
    try:
        user_id = current_user.get("user_id")
        if not user_id:
            raise HTTPException(status_code=400, detail="Token utilisateur invalide")

        # Récupérer l'espace de travail de l'utilisateur via UserWorkspace
        user_workspace_result = await db.execute(
            select(UserWorkspace).where(
                and_(
                    UserWorkspace.user_id == user_id,
                    UserWorkspace.is_active == True
                )
            ).limit(1)
        )
        user_workspace = user_workspace_result.scalar_one_or_none()
        
        if not user_workspace:
            raise HTTPException(status_code=404, detail="Aucun espace de travail trouvé pour cet utilisateur")

        workspace_id = user_workspace.workspace_id

        # Récupérer la TCompany existante
        result = await db.execute(
            select(TCompany).where(
                and_(
                    TCompany.id == tcompany_id,
                    TCompany.workspace_id == workspace_id
                )
            )
        )
        db_tcompany = result.scalar_one_or_none()

        if not db_tcompany:
            raise HTTPException(status_code=404, detail="TCompany non trouvée")

        # Mettre à jour les champs
        update_data = tcompany_in.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_tcompany, field, value)

        await db.commit()
        await db.refresh(db_tcompany)

        print(f"✅ TCompany {tcompany_id} mise à jour")
        
        # Convertir en schéma Pydantic pour éviter les erreurs DetachedInstanceError
        return TCompanyResponse.from_orm(db_tcompany)

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Erreur PUT /tcompanies/{tcompany_id}: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Erreur lors de la mise à jour de la TCompany: {str(e)}")


@router.delete("/{tcompany_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_tcompany(
    *,
    tcompany_id: int,
    db: AsyncSession = Depends(deps.get_db),
    current_user: Dict[str, Any] = Depends(require_auth),
):
    """Supprimer une TCompany (soft delete)"""
    try:
        user_id = current_user.get("user_id")
        if not user_id:
            raise HTTPException(status_code=400, detail="Token utilisateur invalide")

        # Récupérer l'espace de travail de l'utilisateur via UserWorkspace
        user_workspace_result = await db.execute(
            select(UserWorkspace).where(
                and_(
                    UserWorkspace.user_id == user_id,
                    UserWorkspace.is_active == True
                )
            ).limit(1)
        )
        user_workspace = user_workspace_result.scalar_one_or_none()
        
        if not user_workspace:
            raise HTTPException(status_code=404, detail="Aucun espace de travail trouvé pour cet utilisateur")

        workspace_id = user_workspace.workspace_id

        # Récupérer la TCompany
        result = await db.execute(
            select(TCompany).where(
                and_(
                    TCompany.id == tcompany_id,
                    TCompany.workspace_id == workspace_id
                )
            )
        )
        db_tcompany = result.scalar_one_or_none()

        if not db_tcompany:
            raise HTTPException(status_code=404, detail="TCompany non trouvée")

        # Soft delete
        db_tcompany.is_active = False
        await db.commit()

        print(f"✅ TCompany {tcompany_id} supprimée (soft delete)")

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Erreur DELETE /tcompanies/{tcompany_id}: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Erreur lors de la suppression de la TCompany: {str(e)}")
