import { useState } from 'react'
import { Editor } from '@tiptap/react'
import { generateCoverPageHTML, CoverPageData } from '@/services/coverPageService'
import { TCompany } from '@/types/tcompany'
import { Lot } from '@/types/lot'

interface UseCoverPageGenerationProps {
  editor: Editor | null
  lotId?: number | null
  documentType: string
  documentIndice?: string
}

export const useCoverPageGeneration = ({
  editor,
  lotId,
  documentType,
  documentIndice = "01"
}: UseCoverPageGenerationProps) => {
  const [isGenerating, setIsGenerating] = useState(false)

  const fetchLotData = async (lotId: number): Promise<Lot> => {
    const response = await fetch(`/api/lots/${lotId}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })
    if (!response.ok) {
      throw new Error('Erreur lors de la récupération du lot')
    }
    return response.json()
  }

  const fetchLotCompanies = async (lotId: number): Promise<TCompany[]> => {
    const response = await fetch(`/api/technical-documents/lot/${lotId}/companies`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })
    if (!response.ok) {
      throw new Error('Erreur lors de la récupération des entreprises')
    }
    return response.json()
  }

  const generateCoverPage = async () => {
    if (!editor || !lotId) {
      console.error('Éditeur ou ID de lot manquant')
      return
    }

    setIsGenerating(true)
    try {
      // Récupérer les données du lot
      const lot = await fetchLotData(lotId)
      
      // Récupérer les entreprises associées au lot
      const companies = await fetchLotCompanies(lotId)

      // Préparer les données pour la page de garde
      const coverPageData: CoverPageData = {
        lot,
        companies,
        projectTitle: lot.project?.name || `Projet ${lot.project?.id}`,
        documentType,
        documentIndice,
        date: new Date().toLocaleDateString('fr-FR')
      }

      // Générer le HTML de la page de garde
      const coverPageHTML = generateCoverPageHTML(coverPageData)

      // Insérer la page de garde au début du document
      const currentContent = editor.getHTML()
      const newContent = coverPageHTML + currentContent

      // Mettre à jour le contenu de l'éditeur
      editor.commands.setContent(newContent)

      console.log('Page de garde générée avec succès')
    } catch (error) {
      console.error('Erreur lors de la génération de la page de garde:', error)
      alert('Erreur lors de la génération de la page de garde. Veuillez réessayer.')
    } finally {
      setIsGenerating(false)
    }
  }

  return {
    generateCoverPage,
    isGenerating
  }
}
