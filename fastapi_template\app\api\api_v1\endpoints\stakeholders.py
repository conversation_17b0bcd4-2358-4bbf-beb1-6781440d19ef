# app/api/api_v1/endpoints/stakeholders.py
from typing import List, Optional, Any, Dict
from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.ext.asyncio import AsyncSession

from app.api import deps
from app.middleware.auth_sync_middleware import require_auth
from app.crud.stakeholder import stakeholder_crud
from sqlalchemy import select
from app.models.rbac import Role
from app.schemas.stakeholder import (
    StakeholderCreate, 
    StakeholderUpdate, 
    StakeholderResponse, 
    StakeholderStats,
    BulkStakeholderCreate,
    BulkStakeholderResponse
)

router = APIRouter()

@router.get("/lot/{lot_id}/stakeholders", response_model=List[StakeholderResponse])
async def get_lot_stakeholders(
    lot_id: int,
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(deps.get_db),
    current_user: Dict[str, Any] = Depends(require_auth)
):
    """
    Récupérer tous les intervenants d'un lot
    Label français: Intervenants
    """
    try:
        user_id = current_user.get("id") or current_user.get("user_id")
        if not user_id:
            raise HTTPException(status_code=400, detail="Token utilisateur invalide")

        # Récupérer l'espace de travail de l'utilisateur
        from app.models.workspace import UserWorkspace
        from sqlalchemy import select, and_
        from sqlalchemy.orm import selectinload
        
        user_workspace_result = await db.execute(
            select(UserWorkspace).where(
                and_(
                    UserWorkspace.user_id == user_id,
                    UserWorkspace.is_active == True
                )
            ).limit(1)
        )
        user_workspace = user_workspace_result.scalar_one_or_none()
        
        if not user_workspace:
            raise HTTPException(status_code=404, detail="Aucun espace de travail trouvé pour cet utilisateur")

        workspace_id = user_workspace.workspace_id

        # Récupérer les stakeholders du lot avec leurs relations
        from app.models.lot import Stakeholder
        
        result = await db.execute(
            select(Stakeholder).options(
                selectinload(Stakeholder.company),
                selectinload(Stakeholder.lot)
            ).where(
                and_(
                    Stakeholder.lot_id == lot_id,
                    Stakeholder.is_active == True
                )
            ).offset(skip).limit(limit)
        )
        stakeholders = result.scalars().all()

        return stakeholders

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Erreur GET /lot/{lot_id}/stakeholders: {e}")
        raise HTTPException(status_code=500, detail=f"Erreur lors de la récupération des intervenants: {str(e)}")

@router.get("/company/{company_id}/stakeholders", response_model=List[StakeholderResponse])
async def get_company_stakeholders(
    company_id: int,
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(deps.get_db),
    current_user: Dict[str, Any] = Depends(require_auth)
):
    """
    Récupérer tous les lots où une entreprise est intervenante
    """
    stakeholders = stakeholder_crud.get_stakeholders_by_company(
        db=db, 
        company_id=company_id, 
        skip=skip, 
        limit=limit
    )
    return stakeholders

@router.get("/stakeholders/{stakeholder_id}", response_model=StakeholderResponse)
async def get_stakeholder(
    stakeholder_id: int,
    db: AsyncSession = Depends(deps.get_db),
    current_user: Dict[str, Any] = Depends(require_auth)
):
    """
    Récupérer un intervenant par ID
    """
    stakeholder = stakeholder_crud.get_stakeholder(db=db, stakeholder_id=stakeholder_id)
    if not stakeholder:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Intervenant non trouvé"
        )
    return stakeholder

@router.post("/stakeholders", response_model=StakeholderResponse, status_code=status.HTTP_201_CREATED)
async def create_stakeholder(
    stakeholder_data: StakeholderCreate,
    db: AsyncSession = Depends(deps.get_db),
    current_user: Dict[str, Any] = Depends(require_auth)
):
    """
    Créer un nouvel intervenant
    Peut soit choisir une entreprise existante (company_id) soit en créer une nouvelle (company_data)
    """
    try:
        user_id = current_user.get("id") or current_user.get("user_id")
        if not user_id:
            raise HTTPException(status_code=400, detail="Token utilisateur invalide")

        # Récupérer l'espace de travail de l'utilisateur
        from app.models.workspace import UserWorkspace
        from sqlalchemy import select, and_
        from sqlalchemy.orm import selectinload
        
        user_workspace_result = await db.execute(
            select(UserWorkspace).where(
                and_(
                    UserWorkspace.user_id == user_id,
                    UserWorkspace.is_active == True
                )
            ).limit(1)
        )
        user_workspace = user_workspace_result.scalar_one_or_none()
        
        if not user_workspace:
            raise HTTPException(status_code=404, detail="Aucun espace de travail trouvé pour cet utilisateur")

        workspace_id = user_workspace.workspace_id

        # Logique de création
        from app.models.lot import Stakeholder
        from app.models.tcompany import TCompany
        
        company_id = stakeholder_data.company_id

        # Si pas d'entreprise spécifiée, créer une nouvelle entreprise
        if not company_id and stakeholder_data.company_data:
            # Créer une nouvelle TCompany
            new_company = TCompany(
                **stakeholder_data.company_data.dict(),
                workspace_id=workspace_id,
                created_by=user_id
            )
            db.add(new_company)
            await db.flush()  # Pour obtenir l'ID
            company_id = new_company.id
        elif not company_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Vous devez soit spécifier company_id soit fournir company_data"
            )

        # Vérifier que l'entreprise existe
        company_result = await db.execute(
            select(TCompany).where(TCompany.id == company_id)
        )
        company = company_result.scalar_one_or_none()
        if not company:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Entreprise avec l'ID {company_id} non trouvée"
            )

        # Vérifier que l'entreprise n'est pas déjà intervenante sur ce lot
        existing_result = await db.execute(
            select(Stakeholder).where(
                and_(
                    Stakeholder.lot_id == stakeholder_data.lot_id,
                    Stakeholder.company_id == company_id,
                    Stakeholder.is_active == True
                )
            )
        )
        existing = existing_result.scalar_one_or_none()
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"L'entreprise {company.company_name} est déjà intervenante sur ce lot"
            )

        # Créer le stakeholder
        db_stakeholder = Stakeholder(
            lot_id=stakeholder_data.lot_id,
            company_id=company_id,
            role=stakeholder_data.role,
            is_active=stakeholder_data.is_active if stakeholder_data.is_active is not None else True,
            created_by=user_id
        )

        db.add(db_stakeholder)
        await db.commit()
        
        # Récupérer le stakeholder créé avec ses relations
        result = await db.execute(
            select(Stakeholder).options(
                selectinload(Stakeholder.company),
                selectinload(Stakeholder.lot)
            ).where(Stakeholder.id == db_stakeholder.id)
        )
        created_stakeholder = result.scalar_one()

        return created_stakeholder

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Erreur POST /stakeholders: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Erreur lors de la création de l'intervenant: {str(e)}")

@router.put("/stakeholders/{stakeholder_id}", response_model=StakeholderResponse)
async def update_stakeholder(
    stakeholder_id: int,
    stakeholder_data: StakeholderUpdate,
    db: AsyncSession = Depends(deps.get_db),
    current_user: Dict[str, Any] = Depends(require_auth)
):
    """
    Mettre à jour un intervenant
    """
    stakeholder = stakeholder_crud.update_stakeholder(
        db=db,
        stakeholder_id=stakeholder_id,
        stakeholder_data=stakeholder_data
    )
    if not stakeholder:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Intervenant non trouvé"
        )
    return stakeholder

@router.delete("/stakeholders/{stakeholder_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_stakeholder(
    stakeholder_id: int,
    hard_delete: bool = False,
    db: AsyncSession = Depends(deps.get_db),
    current_user: Dict[str, Any] = Depends(require_auth)
):
    """
    Supprimer un intervenant
    Par défaut, suppression logique (soft delete). Utiliser hard_delete=true pour suppression définitive.
    """
    try:
        user_id = current_user.get("id") or current_user.get("user_id")
        if not user_id:
            raise HTTPException(status_code=400, detail="Token utilisateur invalide")

        from app.models.lot import Stakeholder
        from sqlalchemy import select
        
        # Récupérer le stakeholder
        result = await db.execute(
            select(Stakeholder).where(Stakeholder.id == stakeholder_id)
        )
        db_stakeholder = result.scalar_one_or_none()
        
        if not db_stakeholder:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Intervenant non trouvé"
            )

        if hard_delete:
            # Suppression définitive
            await db.delete(db_stakeholder)
        else:
            # Soft delete
            db_stakeholder.is_active = False
        
        await db.commit()

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Erreur DELETE /stakeholders/{stakeholder_id}: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Erreur lors de la suppression de l'intervenant: {str(e)}")

@router.get("/stakeholders/stats", response_model=StakeholderStats)
async def get_stakeholder_stats(
    lot_id: Optional[int] = None,
    db: AsyncSession = Depends(deps.get_db),
    current_user: Dict[str, Any] = Depends(require_auth)
):
    """
    Obtenir des statistiques sur les intervenants
    """
    stats = stakeholder_crud.get_stakeholder_stats(db=db, lot_id=lot_id)
    return stats

@router.post("/stakeholders/bulk", response_model=BulkStakeholderResponse)
async def bulk_create_stakeholders(
    bulk_data: BulkStakeholderCreate,
    db: AsyncSession = Depends(deps.get_db),
    current_user: Dict[str, Any] = Depends(require_auth)
):
    """
    Créer plusieurs intervenants en une fois
    """
    workspace_id = current_user.get("workspace_id", 1)  # Valeur par défaut temporaire
    
    result = stakeholder_crud.bulk_create_stakeholders(
        db=db,
        stakeholders_data=bulk_data.stakeholders,
        current_user_id=current_user["id"],
        workspace_id=workspace_id
    )
    return result

# Routes de compatibilité (à supprimer après migration complète)
@router.get("/lot/{lot_id}/intervenants", response_model=List[StakeholderResponse])
async def get_lot_intervenants_compat(
    lot_id: int,
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(deps.get_db),
    current_user: Dict[str, Any] = Depends(require_auth)
):
    """Route de compatibilité - utiliser /lot/{lot_id}/stakeholders à la place"""
    return await get_lot_stakeholders(lot_id, skip, limit, db, current_user)

@router.post("/intervenants", response_model=StakeholderResponse, status_code=status.HTTP_201_CREATED)
async def create_intervenant_compat(
    stakeholder_data: StakeholderCreate,
    db: AsyncSession = Depends(deps.get_db),
    current_user: Dict[str, Any] = Depends(require_auth)
):
    """Route de compatibilité - utiliser /stakeholders à la place"""
    return await create_stakeholder(stakeholder_data, db, current_user)

@router.get("/roles", response_model=List[Dict[str, Any]])
async def get_business_roles(
    db: AsyncSession = Depends(deps.get_db),
    current_user: Dict[str, Any] = Depends(require_auth)
):
    """
    Récupérer tous les rôles métier (à partir de l'ID 6)
    Ces rôles sont utilisés pour les intervenants des lots
    """
    try:
        # Récupérer les rôles métier (ID >= 6)
        result = await db.execute(
            select(Role).where(Role.id >= 6).order_by(Role.id)
        )
        roles = result.scalars().all()

        # Convertir en liste de dictionnaires
        roles_list = [
            {
                "id": role.id,
                "name": role.name,
                "description": role.description
            }
            for role in roles
        ]

        return roles_list

    except Exception as e:
        print(f"❌ Erreur GET /stakeholders/roles: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Erreur lors de la récupération des rôles métier: {str(e)}"
        )
