import { Editor } from '@tiptap/react'
import { useState, useRef, useEffect } from 'react'
import { extractHeadingNumbers, calculateSimpleIncrement } from '../utils/headingNumbering'

export interface HeadingOption {
  id: string
  title: string
  level: number
  position: number
  number: string
}

interface UseEditorActionsProps {
  editor: Editor | null
  onSave?: () => Promise<void>
  onClose?: () => void
  hasUnsavedChanges?: boolean
}

export function useEditorActions({
  editor,
  onSave,
  onClose,
  hasUnsavedChanges = false
}: UseEditorActionsProps) {
  const [isSaving, setIsSaving] = useState(false)
  const [showCloseConfirm, setShowCloseConfirm] = useState(false)
  const [showImportModal, setShowImportModal] = useState(false)
  const [showAddArticleModal, setShowAddArticleModal] = useState(false)
  const [availableHeadings, setAvailableHeadings] = useState<HeadingOption[]>([])
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Fonction pour extraire les titres disponibles du document
  const extractAvailableHeadings = (): HeadingOption[] => {
    if (!editor) return []

    const headings: HeadingOption[] = []
    const doc = editor.state.doc

    doc.descendants((node, pos) => {
      if (node.type.name === 'heading') {
        const text = node.textContent
        const level = node.attrs.level

        // Extraire le numéro du titre s'il existe
        const match = text.match(/^(\d+(?:\.\d+)*(?:\.[a-z])?)\.?\s*(.*)/)
        const number = match ? match[1] : ''
        const title = match ? match[2] : text

        headings.push({
          id: `heading-${pos}`,
          title: title || 'Titre sans nom',
          level,
          position: pos,
          number
        })
      }
    })

    return headings
  }

  // Mettre à jour les titres disponibles quand le contenu change
  useEffect(() => {
    if (editor) {
      const updateHeadings = () => {
        setAvailableHeadings(extractAvailableHeadings())
      }

      // Mettre à jour immédiatement
      updateHeadings()

      // Écouter les changements
      editor.on('update', updateHeadings)

      return () => {
        editor.off('update', updateHeadings)
      }
    }
  }, [editor])

  // Fonction pour insérer un titre numéroté avec incrémentation automatique
  const insertNumberedHeading = (level: number) => {
    if (!editor) return

    console.log('🚀 Insertion titre niveau:', level)

    // Récupérer tous les titres existants dans le document
    const doc = editor.state.doc
    const headings: { level: number; text: string; pos: number }[] = []
    
    doc.descendants((node, pos) => {
      if (node.type.name === 'heading') {
        const text = node.textContent
        const nodeLevel = node.attrs.level
        headings.push({ level: nodeLevel, text, pos })
      }
    })

    console.log('📝 Tous les headings:', headings.map(h => ({ level: h.level, text: h.text })))

    // Extraire les numéros
    const numberedHeadings = extractHeadingNumbers(headings)
    
    // Calculer le prochain numéro (mode simple pour l'instant)
    const newNumber = calculateSimpleIncrement(numberedHeadings, level)
    
    // Insérer le titre avec le bon numéro
    const headingText = `${newNumber} Nouveau titre`
    editor.chain().focus().toggleHeading({ level: level as 1 | 2 | 3 | 4 | 5 | 6 }).run()
    editor.commands.insertContent(headingText)
    
    // Sélectionner le texte "Nouveau titre" pour permettre la modification
    const currentPos = editor.state.selection.from
    const startPos = currentPos - headingText.length + newNumber.length + 1
    const endPos = currentPos
    editor.commands.setTextSelection({ from: startPos, to: endPos })
  }

  // Initialiser structure CCTP
  const initializeCCTPStructure = () => {
    if (!editor) return
    
    const cctpContent = `
      <h1 style="text-align: center; color: #0F766E;">CAHIER DES CLAUSES TECHNIQUES PARTICULIÈRES</h1>
      <h2>1. Objet du marché</h2>
      <p>Description générale des travaux...</p>
      <h2>2. Dispositions générales</h2>
      <h3>2.1 Réglementation</h3>
      <p>Textes réglementaires applicables...</p>
      <h3>2.2 Normes</h3>
      <p>Normes techniques à respecter...</p>
      <h2>3. Description des ouvrages</h2>
      <h3>3.1 Matériaux</h3>
      <p>Spécifications des matériaux...</p>
      <h3>3.2 Mise en œuvre</h3>
      <p>Modalités de mise en œuvre...</p>
    `
    
    editor.commands.setContent(cctpContent)
  }

  // Gestion de la sauvegarde
  const handleSave = async () => {
    if (!onSave || isSaving) return
    
    setIsSaving(true)
    try {
      await onSave()
      console.log('✅ Document sauvegardé')
    } catch (error) {
      console.error('❌ Erreur lors de la sauvegarde:', error)
      alert('Erreur lors de la sauvegarde')
    } finally {
      setIsSaving(false)
    }
  }

  // Gestion de la fermeture
  const handleClose = () => {
    if (hasUnsavedChanges) {
      setShowCloseConfirm(true)
    } else {
      onClose?.()
    }
  }

  const confirmClose = () => {
    setShowCloseConfirm(false)
    onClose?.()
  }

  const cancelClose = () => {
    setShowCloseConfirm(false)
  }

  // Gestion de l'import de fichier
  const handleFileImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file || !editor) return

    const reader = new FileReader()
    reader.onload = (e) => {
      const content = e.target?.result as string
      // Ici on pourrait ajouter une logique de conversion Word vers HTML
      editor.commands.insertContent(content)
    }
    reader.readAsText(file)
    
    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  return {
    // États
    isSaving,
    showCloseConfirm,
    showImportModal,
    showAddArticleModal,
    availableHeadings,

    // Setters
    setShowImportModal,
    setShowAddArticleModal,

    // Actions
    insertNumberedHeading,
    initializeCCTPStructure,
    handleSave,
    handleClose,
    confirmClose,
    cancelClose,
    handleFileImport,

    // Refs
    fileInputRef
  }
}
