import { TCompany } from '@/types/tcompany'
import { Lot } from '@/types/lot'

export interface CoverPageData {
  lot: Lot
  companies: TCompany[]
  projectTitle: string
  documentType: string
  documentIndice: string
  date: string
}

export interface CoverPageTemplate {
  title: string
  subtitle: string
  maitreDOuvrage: {
    name: string
    logo?: string
    address: string
  }
  maitriseOeuvre: {
    name: string
    logo?: string
    address: string
    contact: string
  }
  otherStakeholders: Array<{
    role: string
    name: string
    address?: string
  }>
  lotInfo: {
    title: string
    description: string
  }
  documentInfo: {
    type: string
    indice: string
    date: string
  }
}

export const generateCoverPageHTML = (data: CoverPageData): string => {
  const template: CoverPageTemplate = {
    title: data.projectTitle,
    subtitle: `Centre ${data.lot.project?.workspace?.name || 'WORKSPACE'}`,
    maitreDOuvrage: {
      name: data.companies.find(c => c.activity?.includes('MOA'))?.company_name || 'MAÎTRE D\'OUVRAGE',
      logo: data.companies.find(c => c.activity?.includes('MOA'))?.logo_url,
      address: data.companies.find(c => c.activity?.includes('MOA'))?.address || ''
    },
    maitriseOeuvre: {
      name: data.companies.find(c => c.activity?.includes('ARCHI') || c.activity?.includes('BE'))?.company_name || 'MAÎTRISE D\'ŒUVRE',
      logo: data.companies.find(c => c.activity?.includes('ARCHI') || c.activity?.includes('BE'))?.logo_url,
      address: data.companies.find(c => c.activity?.includes('ARCHI') || c.activity?.includes('BE'))?.address || '',
      contact: data.companies.find(c => c.activity?.includes('ARCHI') || c.activity?.includes('BE'))?.email || ''
    },
    otherStakeholders: data.companies
      .filter(c => !c.activity?.includes('MOA') && !c.activity?.includes('ARCHI') && !c.activity?.includes('BE'))
      .map(c => ({
        role: c.activity || 'Intervenant',
        name: c.company_name,
        address: c.address
      })),
    lotInfo: {
      title: data.lot.name || `LOT ${data.lot.id}`,
      description: data.lot.description || 'Description du lot'
    },
    documentInfo: {
      type: data.documentType,
      indice: data.documentIndice,
      date: data.date
    }
  }

  return `
    <div style="page-break-after: always; font-family: Arial, sans-serif; padding: 20px;">
      <!-- Titre principal -->
      <div style="text-align: center; border: 2px solid #000; padding: 20px; margin-bottom: 20px;">
        <h1 style="margin: 0; font-size: 18px; font-weight: bold;">${template.title}</h1>
        <h2 style="margin: 10px 0 0 0; font-size: 14px; font-weight: normal;">${template.subtitle}</h2>
      </div>

      <!-- Maître d'ouvrage -->
      <div style="border: 1px solid #000; margin-bottom: 10px;">
        <div style="background-color: #f0f0f0; padding: 8px; text-align: center; font-weight: bold; border-bottom: 1px solid #000;">
          MAÎTRE D'OUVRAGE
        </div>
        <div style="padding: 15px; display: flex; align-items: center;">
          ${template.maitreDOuvrage.logo ? 
            `<img src="${template.maitreDOuvrage.logo}" alt="Logo" style="max-height: 60px; margin-right: 20px;" />` : 
            '<div style="width: 100px; height: 60px; background-color: #e0e0e0; margin-right: 20px; display: flex; align-items: center; justify-content: center; font-size: 12px;">Logo</div>'
          }
          <div>
            <div style="font-weight: bold; margin-bottom: 5px;">${template.maitreDOuvrage.name}</div>
            <div style="font-size: 12px;">${template.maitreDOuvrage.address}</div>
          </div>
        </div>
      </div>

      <!-- Maîtrise d'œuvre -->
      <div style="border: 1px solid #000; margin-bottom: 20px;">
        <div style="background-color: #f0f0f0; padding: 8px; text-align: center; font-weight: bold; border-bottom: 1px solid #000;">
          MAÎTRISE D'ŒUVRE
        </div>
        <div style="padding: 15px;">
          <table style="width: 100%; border-collapse: collapse;">
            <tr>
              <td style="width: 30%; vertical-align: top; padding-right: 15px;">
                <strong>Maîtrise d'œuvre</strong>
              </td>
              <td style="width: 70%; vertical-align: top;">
                ${template.maitriseOeuvre.logo ? 
                  `<img src="${template.maitriseOeuvre.logo}" alt="Logo" style="max-height: 60px; float: right;" />` : 
                  '<div style="width: 100px; height: 60px; background-color: #e0e0e0; float: right; display: flex; align-items: center; justify-content: center; font-size: 12px;">Logo</div>'
                }
                <div style="font-weight: bold;">${template.maitriseOeuvre.name}</div>
                <div style="font-size: 12px; margin-top: 5px;">${template.maitriseOeuvre.address}</div>
                <div style="font-size: 12px; margin-top: 5px;">📧 ${template.maitriseOeuvre.contact}</div>
              </td>
            </tr>
          </table>
          
          ${template.otherStakeholders.map(stakeholder => `
            <div style="margin-top: 15px; padding-top: 10px; border-top: 1px solid #ddd;">
              <div style="font-weight: bold;">${stakeholder.role}</div>
              <div style="margin-top: 5px;">${stakeholder.name}</div>
              ${stakeholder.address ? `<div style="font-size: 12px; margin-top: 2px;">${stakeholder.address}</div>` : ''}
            </div>
          `).join('')}
        </div>
      </div>

      <!-- Informations du lot -->
      <div style="border: 1px solid #000; margin-bottom: 20px;">
        <div style="background-color: #f0f0f0; padding: 8px; text-align: center; font-weight: bold; border-bottom: 1px solid #000;">
          ${template.lotInfo.title}
        </div>
        <div style="padding: 15px; text-align: center;">
          <div style="font-size: 16px; font-weight: bold; margin-bottom: 10px;">${template.lotInfo.description}</div>
          <div style="font-size: 14px; font-weight: bold;">Cahier des Clauses Techniques Particulières</div>
        </div>
      </div>

      <!-- Tableau des modifications -->
      <table style="width: 100%; border-collapse: collapse; border: 1px solid #000; margin-bottom: 20px;">
        <thead>
          <tr style="background-color: #f0f0f0;">
            <th style="border: 1px solid #000; padding: 8px; text-align: center; width: 15%;">Indice</th>
            <th style="border: 1px solid #000; padding: 8px; text-align: center; width: 20%;">Date</th>
            <th style="border: 1px solid #000; padding: 8px; text-align: center; width: 65%;">Modification</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td style="border: 1px solid #000; padding: 8px; text-align: center;">${template.documentInfo.indice}</td>
            <td style="border: 1px solid #000; padding: 8px; text-align: center;">${template.documentInfo.date}</td>
            <td style="border: 1px solid #000; padding: 8px; text-align: center;">Édition originale</td>
          </tr>
          <tr>
            <td style="border: 1px solid #000; padding: 8px; height: 30px;"></td>
            <td style="border: 1px solid #000; padding: 8px;"></td>
            <td style="border: 1px solid #000; padding: 8px;"></td>
          </tr>
          <tr>
            <td style="border: 1px solid #000; padding: 8px; height: 30px;"></td>
            <td style="border: 1px solid #000; padding: 8px;"></td>
            <td style="border: 1px solid #000; padding: 8px;"></td>
          </tr>
        </tbody>
      </table>

      <!-- Tableau récapitulatif -->
      <table style="width: 100%; border-collapse: collapse; border: 1px solid #000;">
        <thead>
          <tr style="background-color: #f0f0f0;">
            <th style="border: 1px solid #000; padding: 8px; text-align: center; width: 15%;">Affaire</th>
            <th style="border: 1px solid #000; padding: 8px; text-align: center; width: 15%;">Type</th>
            <th style="border: 1px solid #000; padding: 8px; text-align: center; width: 15%;">Phase</th>
            <th style="border: 1px solid #000; padding: 8px; text-align: center; width: 15%;">N° lot</th>
            <th style="border: 1px solid #000; padding: 8px; text-align: center; width: 15%;">Indice</th>
            <th style="border: 1px solid #000; padding: 8px; text-align: center; width: 25%;">Date</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td style="border: 1px solid #000; padding: 8px; text-align: center;">${data.lot.project?.id || 'N/A'}</td>
            <td style="border: 1px solid #000; padding: 8px; text-align: center;">${template.documentInfo.type}</td>
            <td style="border: 1px solid #000; padding: 8px; text-align: center;">DCE</td>
            <td style="border: 1px solid #000; padding: 8px; text-align: center;">${data.lot.id}</td>
            <td style="border: 1px solid #000; padding: 8px; text-align: center;">${template.documentInfo.indice}</td>
            <td style="border: 1px solid #000; padding: 8px; text-align: center;">${template.documentInfo.date}</td>
          </tr>
        </tbody>
      </table>
    </div>
  `
}
