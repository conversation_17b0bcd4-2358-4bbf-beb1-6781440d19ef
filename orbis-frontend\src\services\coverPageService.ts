import { TCompany } from '@/types/tcompany'
import { Lot } from '@/types/lot'

export interface CoverPageData {
  lot: Lot
  companies: TCompany[]
  projectTitle: string
  documentType: string
  documentIndice: string
  date: string
}

export interface CoverPageTemplate {
  title: string
  subtitle: string
  maitreDOuvrage: {
    name: string
    logo?: string
    address: string
  }
  maitriseOeuvre: {
    name: string
    logo?: string
    address: string
    contact: string
  }
  otherStakeholders: Array<{
    role: string
    name: string
    address?: string
  }>
  lotInfo: {
    title: string
    description: string
  }
  documentInfo: {
    type: string
    indice: string
    date: string
  }
}

export const generateCoverPageHTML = (data: CoverPageData): string => {
  console.log('🔍 Données reçues pour la page de garde:', data)

  const template: CoverPageTemplate = {
    title: data.lot.project?.name || data.projectTitle || `Projet ${data.lot.project?.id || 'N/A'}`,
    subtitle: ``, // TODO: récupérer le nom du workspace
    maitreDOuvrage: {
      name: data.companies.find(c => c.role?.includes('MOA'))?.company_name || 'Non renseigné',
      logo: data.companies.find(c => c.role?.includes('MOA'))?.logo_url,
      address: data.companies.find(c => c.role?.includes('MOA'))?.address || ''
    },
    maitriseOeuvre: {
      name: data.companies.find(c => c.role?.includes('ARCHI') || c.role?.includes('BE'))?.company_name || 'Non renseigné',
      logo: data.companies.find(c => c.role?.includes('ARCHI') || c.role?.includes('BE'))?.logo_url,
      address: data.companies.find(c => c.role?.includes('ARCHI') || c.role?.includes('BE'))?.address || '',
      contact: data.companies.find(c => c.role?.includes('ARCHI') || c.role?.includes('BE'))?.email || ''
    },
    otherStakeholders: data.companies
      .filter(c => !c.role?.includes('MOA') && !c.role?.includes('ARCHI') && !c.role?.includes('BE'))
      .map(c => ({
        role: c.role || 'Intervenant',
        name: c.company_name,
        address: c.address
      })),
    lotInfo: {
      title: data.lot.name || `LOT ${data.lot.id}`,
      description: data.lot.description || 'Description du lot'
    },
    documentInfo: {
      type: data.documentType,
      indice: data.documentIndice,
      date: data.date
    }
  }

  console.log('📋 Template généré:', template)

  return `
    <div style="page-break-after: always; font-family: Arial, sans-serif; padding: 0; margin: 0;">
      <!-- Titre principal -->
      <div style="text-align: center; border: 2px solid #000; padding: 30px; margin-bottom: 10px;">
        <h1 style="margin: 0; font-size: 20px; font-weight: bold; color: #0F766E;">${template.title}</h1>
        <h2 style="margin: 15px 0 0 0; font-size: 16px; font-weight: normal; color: #333333;">${template.subtitle}</h2>
      </div>

      <!-- Maître d'ouvrage -->
      <div style="border: 2px solid #000; margin-bottom: 10px;">
        <div style="background-color: #f0f0f0; padding: 12px; text-align: center; font-weight: bold; border-bottom: 2px solid #000; font-size: 14px;">
          MAÎTRE D'OUVRAGE
        </div>
        <div style="padding: 25px; text-align: center;">
          ${template.maitreDOuvrage.logo ?
            `<img src="${template.maitreDOuvrage.logo}" alt="Logo" style="max-height: 80px; margin-bottom: 15px;" />` :
            '<div style="width: 120px; height: 80px; background-color: #e0e0e0; margin: 0 auto 15px auto; display: flex; align-items: center; justify-content: center; font-size: 12px; border: 1px solid #ccc;">Logo</div>'
          }
          <div style="font-weight: bold; font-size: 16px; margin-bottom: 8px;">${template.maitreDOuvrage.name}</div>
          <div style="font-size: 12px; color: #666;">${template.maitreDOuvrage.address}</div>
        </div>
      </div>

      <!-- Maîtrise d'œuvre -->
      <div style="border: 2px solid #000; margin-bottom: 20px;">
        <div style="background-color: #f0f0f0; padding: 12px; text-align: center; font-weight: bold; border-bottom: 2px solid #000; font-size: 14px;">
          MAÎTRISE D'ŒUVRE
        </div>
        <div style="padding: 20px;">
          <table style="width: 100%; border-collapse: collapse; margin-bottom: 15px;">
            <tr>
              <td style="width: 40%; vertical-align: top; padding-right: 20px; text-align: center;">
                <div style="font-weight: bold; margin-bottom: 10px; font-size: 14px;">Maîtrise d'œuvre</div>
                ${template.maitriseOeuvre.logo ?
                  `<img src="${template.maitriseOeuvre.logo}" alt="Logo" style="max-height: 80px; margin-bottom: 10px;" />` :
                  '<div style="width: 120px; height: 80px; background-color: #e0e0e0; margin: 0 auto 10px auto; display: flex; align-items: center; justify-content: center; font-size: 12px; border: 1px solid #ccc;">Logo</div>'
                }
              </td>
              <td style="width: 60%; vertical-align: top; text-align: center;">
                <div style="font-weight: bold; font-size: 16px; margin-bottom: 8px;">${template.maitriseOeuvre.name}</div>
                <div style="font-size: 12px; margin-bottom: 5px; color: #666;">${template.maitriseOeuvre.address}</div>
                <div style="font-size: 12px; color: #666;">📧 ${template.maitriseOeuvre.contact}</div>
              </td>
            </tr>
          </table>

          ${template.otherStakeholders.map(stakeholder => `
            <div style="margin-bottom: 15px; padding: 10px; border: 1px solid #ddd; text-align: center;">
              <div style="font-weight: bold; margin-bottom: 5px; color: #0F766E;">${stakeholder.role}</div>
              <div style="font-size: 14px; margin-bottom: 3px;">${stakeholder.name}</div>
              ${stakeholder.address ? `<div style="font-size: 12px; color: #666;">${stakeholder.address}</div>` : ''}
            </div>
          `).join('')}
        </div>
      </div>

      <!-- Informations du lot -->
      <div style="border: 2px solid #000; margin-bottom: 20px;">
        <div style="background-color: #f0f0f0; padding: 12px; text-align: center; font-weight: bold; border-bottom: 2px solid #000; font-size: 16px;">
          ${template.lotInfo.title}
        </div>
        <div style="padding: 25px; text-align: center;">
          <div style="font-size: 18px; font-weight: bold; margin-bottom: 15px; color: #0F766E;">${template.lotInfo.description}</div>
          <div style="font-size: 16px; font-weight: bold; color: #333333;">Cahier des Clauses Techniques Particulières</div>
        </div>
      </div>

      <!-- Tableau des modifications -->
      <table style="width: 100%; border-collapse: collapse; border: 1px solid #000; margin-bottom: 20px;">
        <thead>
          <tr style="background-color: #f0f0f0;">
            <th style="border: 1px solid #000; padding: 8px; text-align: center; width: 15%;">Indice</th>
            <th style="border: 1px solid #000; padding: 8px; text-align: center; width: 20%;">Date</th>
            <th style="border: 1px solid #000; padding: 8px; text-align: center; width: 65%;">Modification</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td style="border: 1px solid #000; padding: 8px; text-align: center;">${template.documentInfo.indice}</td>
            <td style="border: 1px solid #000; padding: 8px; text-align: center;">${template.documentInfo.date}</td>
            <td style="border: 1px solid #000; padding: 8px; text-align: center;">Édition originale</td>
          </tr>
          <tr>
            <td style="border: 1px solid #000; padding: 8px; height: 30px;"></td>
            <td style="border: 1px solid #000; padding: 8px;"></td>
            <td style="border: 1px solid #000; padding: 8px;"></td>
          </tr>
          <tr>
            <td style="border: 1px solid #000; padding: 8px; height: 30px;"></td>
            <td style="border: 1px solid #000; padding: 8px;"></td>
            <td style="border: 1px solid #000; padding: 8px;"></td>
          </tr>
        </tbody>
      </table>

      <!-- Tableau récapitulatif -->
      <table style="width: 100%; border-collapse: collapse; border: 1px solid #000;">
        <thead>
          <tr style="background-color: #f0f0f0;">
            <th style="border: 1px solid #000; padding: 8px; text-align: center; width: 15%;">Affaire</th>
            <th style="border: 1px solid #000; padding: 8px; text-align: center; width: 15%;">Type</th>
            <th style="border: 1px solid #000; padding: 8px; text-align: center; width: 15%;">Phase</th>
            <th style="border: 1px solid #000; padding: 8px; text-align: center; width: 15%;">N° lot</th>
            <th style="border: 1px solid #000; padding: 8px; text-align: center; width: 15%;">Indice</th>
            <th style="border: 1px solid #000; padding: 8px; text-align: center; width: 25%;">Date</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td style="border: 1px solid #000; padding: 8px; text-align: center;">${data.lot.project?.id || 'N/A'}</td>
            <td style="border: 1px solid #000; padding: 8px; text-align: center;">${template.documentInfo.type}</td>
            <td style="border: 1px solid #000; padding: 8px; text-align: center;">DCE</td>
            <td style="border: 1px solid #000; padding: 8px; text-align: center;">${data.lot.id}</td>
            <td style="border: 1px solid #000; padding: 8px; text-align: center;">${template.documentInfo.indice}</td>
            <td style="border: 1px solid #000; padding: 8px; text-align: center;">${template.documentInfo.date}</td>
          </tr>
        </tbody>
      </table>
    </div>
  `
}
